using UnityEngine;
using UnityEngine.Tilemaps;

namespace Utils.Extensions
{
    public static class TilemapExtensions
    {
        public static Rect GetTileMapRect(this Tilemap tilemap)
        {
            var boundsRect = tilemap.localBounds.ToRect();

            var angle = tilemap.transform.rotation.eulerAngles.z;

            var pivot = boundsRect.center;

            var newPivot = pivot.Rotate(angle);

            var swapDimensions = angle / 90f % 2 == 1;

            var finalRect = boundsRect;

            if (swapDimensions)
            {
                var tmp = finalRect.width;
                finalRect.width = finalRect.height;
                finalRect.height = tmp;
            }

            finalRect.center = tilemap.transform.position + (Vector3)newPivot;

            return finalRect;
        }
    }
}