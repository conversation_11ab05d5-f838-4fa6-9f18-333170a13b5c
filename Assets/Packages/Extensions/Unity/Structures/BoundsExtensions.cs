using System.Collections.Generic;
using System.Runtime.CompilerServices;

using UnityEngine;

namespace Utils.Extensions
{
    public static class BoundsExtensions
    {
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Rect ToRect(this Bounds bounds)
        {
            return new(bounds.min, bounds.size);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Rect ToRect(this BoundsInt bounds)
        {
            return new((Vector3)bounds.min, (Vector3)bounds.size);
        }

        public static List<Vector3> BoundsToPoints(this Bounds bounds)
        {
            var xLeft = bounds.center.x - bounds.extents.x;
            var xRight = bounds.center.x + bounds.extents.x;
            var yTop = bounds.center.y + bounds.extents.y;
            var yBottom = bounds.center.y - bounds.extents.y;

            var vector3List = new List<Vector3>
            {
                new(xLeft, yBottom, 0),
                new(xLeft, yTop, 0),
                new(xRight, yTop, 0),
                new(xRight, yBottom, 0)
            };

            return vector3List;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool Contains(this Bounds bounds, Bounds other)
        {
            return bounds.min.x <= other.min.x && bounds.max.x >= other.max.x &&
                   bounds.min.y <= other.min.y && bounds.max.y >= other.max.y &&
                   bounds.min.z <= other.min.z && bounds.max.z >= other.max.z;
        }
    }
}