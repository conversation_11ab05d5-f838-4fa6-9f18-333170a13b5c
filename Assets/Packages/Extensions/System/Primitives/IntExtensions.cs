using System.Runtime.CompilerServices;

namespace Utils.Extensions
{
    public static class IntExtensions
    {
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void SetBitOne(this ref int _int, int position) => _int |= 1 << position;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void SetBitZero(this ref int _int, int position) => _int &= ~(1 << position);
    }
}