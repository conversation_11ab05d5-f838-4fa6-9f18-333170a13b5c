using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

using UnityEngine;

namespace Utils.Extensions
{
    public static partial class ListExtensions
    {
        public static void Remove<T>(this List<T> list, System.Predicate<T> removeCondition)
        {
            for (var i = list.Count - 1; i >= 0; i--)
            {
                if (removeCondition(list[i]))
                    list.RemoveAt(i);
            }
        }

        public static void RemoveRange<T>(this List<T> list, IEnumerable<T> removeEnumerable)
        {
            foreach (var removeItem in removeEnumerable)
            {
                for (var i = list.Count - 1; i >= 0; i--)
                {
                    if (list[i].Equals(removeItem))
                        list.RemoveAt(i);
                }
            }
        }

        public static void RemoveItemsOfType<T>(this List<T> list, System.Type type)
        {
            for (var i = list.Count - 1; i >= 0; i--)
            {
                if (list[i].GetType() == type)
                    list.RemoveAt(i);
            }
        }

        public static void FilterItemsOfType<T>(this List<T> list, IList<System.Type> allowedTypes)
        {
            for (var i = list.Count - 1; i >= 0; i--)
            {
                if (!allowedTypes.Contains(list[i].GetType()))
                    list.RemoveAt(i);
            }
        }

        public static void AddTimes<T>(this List<T> list, T addedItem, int times)
        {
            for (var i = 0; i < times; i++)
            {
                list.Add(addedItem);
            }
        }

        public static void RemoveNullItems<T>(this List<T> list) where T : class
        {
            for (var i = list.Count - 1; i >= 0; i--)
            {
                if (list[i] == null)
                    list.RemoveAt(i);
            }
        }

        public static T Random<T>(this List<T> list, System.Random random = null)
        {
            var count = list.Count;
            if (count == 0) return default;
            var index = random?.Next(0, count) ?? UnityEngine.Random.Range(0, count);
            return list[index];
        }

        public static void Shuffle<T>(this List<T> list, System.Random random = null)
        {
            var n = list.Count;
            while (n > 1)
            {
                n--;

                int k;
                if (random == null)
                    k = UnityEngine.Random.Range(0, n + 1);
                else
                    k = random.Next(0, n + 1);

                (list[n], list[k]) = (list[k], list[n]);
            }
        }

        public static List<string> ToNames<T>(this List<T> list)
        {
            var names = new List<string>();
            foreach (var item in list)
                names.Add((item as UnityEngine.Object).name);

            return names;
        }

        public static string ToSpacedString<T>(this List<T> list)
        {
            if (list == null)
                return "null";
            else
                return string.Join(" ", list);
        }

        public static string ToSpacedString(this List<RectTransform> list)
        {
            var listToStrings = new List<string>();
            foreach (var entry in list)
                listToStrings.Add(entry.name);

            if (list == null)
                return "null";
            else
                return string.Join(" ", listToStrings);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void SetActive(this List<GameObject> list, bool state)
        {
            for (var i = 0; i < list.Count; i++)
                if (list[i].activeSelf != state)
                    list[i].SetActive(state);
        }

        public static Vector2 Sum(this List<Vector2> list)
        {
            var sum = Vector2.zero;
            foreach (var vector in list)
                sum += vector;
            return sum;
        }

        public static Color GetAverage(this List<Color> colors)
        {
            var count = 0;
            if (colors == null || (count = colors.Count) == 0)
                return Color.white;

            float r = 0;
            float g = 0;
            float b = 0;
            float a = 0;

            for (var i = 0; i < count; i++)
            {
                r += colors[i].r;
                g += colors[i].g;
                b += colors[i].b;
                a += colors[i].a;
            }

            return new Color(r / count, g / count, b / count, a / count);
        }
        
        public static void RemoveBySwap<T>(this List<T> list, int index)
        {
            list[index] = list[^1];
            list.RemoveAt(list.Count - 1);
        }

        public static void RemoveBySwap<T>(this List<T> list, T item)
        {
            var index = list.IndexOf(item);
            RemoveBySwap(list, index);
        }

        public static void RemoveBySwap<T>(this List<T> list, Predicate<T> predicate)
        {
            var index = list.FindIndex(predicate);
            RemoveBySwap(list, index);
        }
    }
}
