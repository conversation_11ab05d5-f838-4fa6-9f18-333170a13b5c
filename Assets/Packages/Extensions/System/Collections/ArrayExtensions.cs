using System.Collections.Generic;

using UnityEngine;

namespace Utils.Extensions
{
    public static class ArrayExtensions
    {
        public static void Shuffle<T>(this T[] array, System.Random random = null)
        {
            int n = array.Length;
            while (n > 1)
            {
                n--;

                int k;
                if (random == null)
                    k = UnityEngine.Random.Range(0, n + 1);
                else
                    k = random.Next(0, n + 1);

                T value = array[k];
                array[k] = array[n];
                array[n] = value;
            }
        }

        public static List<T> ToList<T>(this T[,] array, System.Random random = null)
        {
            List<T> output = new List<T>();

            for (int iY = 0; iY < array.GetLength(0); iY++)
            {
                for (int iX = 0; iX < array.GetLength(1); iX++)
                {
                    output.Add(array[iY, iX]);
                }
            }

            return output;
        }

        /// <summary>
        /// Returns original array of length is correct,
        /// returns new array of 'length' if array is null,
        /// transfers values to new array of 'length' and returns it
        /// </summary>
        public static T[] Resize<T>(this T[] array, int length)
        {
            if (array != null)
                if (array.Length == length)
                    return array;

            var correctArray = new T[length];

            if (array != null)
                for (int i = 0; i < Mathf.Min(array.Length, correctArray.Length); i++)
                    correctArray[i] = array[i];

            return correctArray;
        }

        public static void ClearToDefault<T>(this T[] arr)
        {
            for (int i = 0; i < arr.Length; i++)
                arr[i] = default;
        }
    }
}
