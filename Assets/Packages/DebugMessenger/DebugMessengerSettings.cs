using System;
using System.Collections.Generic;
using System.Linq;

using UnityEngine;
using Sirenix.OdinInspector;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Utils.Debug
{
    [CreateAssetMenu(menuName = "Debug/Debug Messenger Settings", fileName = "DebugMessengerSettings")]
    public class DebugMessengerSettings : SerializedScriptableObject
    {
        [ReadOnly]
        public bool settingsConnected = false;

        public bool writeEnterPlaymodeProfilerData = false;

        [Button]
        public void StartProfilerLog()
        {
            writeEnterPlaymodeProfilerData = true;
            DebugMessenger.StartProfilerLog();
        }

        private const string SettingsFoldoutGroup = "Settings";

        [FoldoutGroup(SettingsFoldoutGroup)]
        [DictionaryDrawerSettings(DisplayMode = DictionaryDisplayOptions.ExpandedFoldout, KeyLabel = "", ValueLabel = "")]
        public Dictionary<Type, DebugLevel> hardSettings = new();

        [FoldoutGroup(SettingsFoldoutGroup)]
        public Dictionary<string, DebugLevel> additionalSettings = new();

        private const string AssignmentFoldoutGroup = "Assignment";

        private const string AssignmentMainFoldoutSubGroup = "/Main";

        [FoldoutGroup(AssignmentFoldoutGroup + AssignmentMainFoldoutSubGroup)]
        [Space]
        [DictionaryDrawerSettings(DisplayMode = DictionaryDisplayOptions.ExpandedFoldout, KeyLabel = "", ValueLabel = "")]
        public Dictionary<Type, DebugLevel> queryEntries = new();

        [Button, FoldoutGroup(AssignmentFoldoutGroup + AssignmentMainFoldoutSubGroup)]
        public void FindEntries(string query)
        {
            queryEntries.Clear();
            var resultsEnumerator = hardSettings.Where(e => e.Key.Name.ToLower().Contains(query.ToLower())).GetEnumerator();
            while (resultsEnumerator.MoveNext())
                queryEntries.Add(resultsEnumerator.Current.Key, resultsEnumerator.Current.Value);
        }

        [Button, FoldoutGroup(AssignmentFoldoutGroup + AssignmentMainFoldoutSubGroup)]
        public void FindEntries(DebugLevel debugLevel)
        {
            queryEntries.Clear();
            var resultsEnumerator = hardSettings.Where(e => e.Value == debugLevel).GetEnumerator();
            while (resultsEnumerator.MoveNext())
                queryEntries.Add(resultsEnumerator.Current.Key, resultsEnumerator.Current.Value);
        }

        [Button, FoldoutGroup(AssignmentFoldoutGroup + AssignmentMainFoldoutSubGroup)]
        public void ApplyEntriesModifications()
        {
            foreach (var entry in queryEntries)
            {
                if (!modifiedEntries.ContainsKey(entry.Key) && hardSettings[entry.Key] != entry.Value)
                    modifiedEntries.Add(entry.Key, hardSettings[entry.Key]);

                hardSettings[entry.Key] = entry.Value;
            }

            queryEntries.Clear();
        }

        private const string AssignmentDiscardFoldoutSubGroup = "/Discard";

        [FoldoutGroup(AssignmentFoldoutGroup + AssignmentDiscardFoldoutSubGroup)]
        [DictionaryDrawerSettings(DisplayMode = DictionaryDisplayOptions.ExpandedFoldout, KeyLabel = "", ValueLabel = "")]
        public Dictionary<Type, DebugLevel> modifiedEntries = new();

        [Button, FoldoutGroup(AssignmentFoldoutGroup + AssignmentDiscardFoldoutSubGroup)]
        public void DiscardModifications()
        {
            foreach (var entry in modifiedEntries)
                hardSettings[entry.Key] = entry.Value;

            modifiedEntries.Clear();
        }

        [Button, FoldoutGroup(AssignmentFoldoutGroup)]
        public void SetDebugLevel(Type type, DebugLevel level = DebugLevel.Warning)
        {
            hardSettings[type] = level;
        }

#if UNITY_EDITOR
        [Button]
        public void MarkDirty()
        {
            EditorUtility.SetDirty(this);
        }
#endif
    }

    public static class DebugMessengerProvider
    {
        #region Debug

        private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
        private static readonly System.Type type = typeof(DebugMessengerProvider);

        #endregion

        private static DebugMessengerSettings SettingsAsset;

#if UNITY_EDITOR
        static DebugMessengerProvider()
        {
            var settingsAssetGuid = AssetDatabase.FindAssets("t: " + typeof(DebugMessengerSettings))[0];
            SettingsAsset = AssetDatabase.LoadAssetAtPath(AssetDatabase.GUIDToAssetPath(settingsAssetGuid), typeof(DebugMessengerSettings)) as DebugMessengerSettings;

            SettingsAsset.settingsConnected = true;

            EditorApplication.playModeStateChanged += ResetSettings;

            if (SettingsAsset.writeEnterPlaymodeProfilerData)
                EditorApplication.playModeStateChanged += StateChange;
        }

        private static void ResetSettings(PlayModeStateChange state)
        {
            using var _ = DebugMessenger.LogTime(DebugLevel.Message, $"{type.Name} : StateChange", $"New state: {state}");

            if (state == PlayModeStateChange.ExitingPlayMode)
                SettingsAsset.settingsConnected = false;
        }

        /// <summary>
        /// https://forum.unity.com/threads/profiler-so-slow-i-cant-even-complain-that-its-slow.1071878/
        /// the 2GB limit is a limitation of the processed .data format
        /// The .raw format generated via Profiling.Profiler.logFile does not have that limitation
        /// </summary>
        private static void StateChange(PlayModeStateChange state)
        {
            //if (state == PlayModeStateChange.ExitingEditMode)
            //    DebugMessager.StartProfilerLog();

            if (state == PlayModeStateChange.EnteredEditMode)
            {
                SettingsAsset.writeEnterPlaymodeProfilerData = false;
                DebugMessenger.EndProfilerLog();
            }
        }
#endif

        /// <summary>
        /// Will provide level from 'hardSettings' if exists, else - save and return 'providedLevel'
        /// </summary>
        /// <returns></returns>
        public static DebugLevel GetLevel(Type type, DebugLevel providedLevel)
        {
            if (SettingsAsset == null)
                return DebugLevel.Error;

            if (!SettingsAsset.hardSettings.ContainsKey(type))
                SettingsAsset.hardSettings[type] = providedLevel;

            return SettingsAsset.hardSettings[type];
        }

        public static DebugLevel GetLevel(string additionalKey, DebugLevel providedLevel)
        {
            if (SettingsAsset == null)
                return DebugLevel.Error;

            if (!SettingsAsset.additionalSettings.ContainsKey(additionalKey))
                SettingsAsset.additionalSettings[additionalKey] = providedLevel;

            return SettingsAsset.additionalSettings[additionalKey];
        }

        /* Example of implementation in target script:

        private static readonly System.Type type = typeof(CLASS_NAME_HERE);
        DebugLevel DebugLevel => DebugMessagerProvider.GetLevel(type, DebugLevel.Message);

        */
    }
}
