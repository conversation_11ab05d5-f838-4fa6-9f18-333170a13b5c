using UnityEngine;

public class ProjectorScreenActivator : GenericInteractable
{
    [SerializeField] private Animator animator;   

    private static readonly int IsOpenHash = Animator.StringToHash("isOpen");

    public override bool RemoteInteractionAllowed => false;

    protected virtual void Start()
    {
        if (animator == null)
        {
            animator = GetComponent<Animator>();
        }
        
        if (GameConfig.Instance.DebugSettings.EnableProjector)
            ToggleOpened();
    }

    public override bool RequestInteraction(InteractAction interactor)
    {
        return true; 
    }

    public override void InteractClientSide(IGrabbable itemInHands, bool isOwner, Vector3 raycastPoint, Vector3 headDirection, ulong clientId, InteractAction interactor)
    {
        ToggleOpened();
    }

    private void ToggleOpened()
    {
        animator.SetBool(IsOpenHash, !animator.GetBool(IsOpenHash));
    }
}