using System.Collections;
using System.Collections.Generic;
using Consts;
using MalteHusung.GlobalMessage;
using Unity.Netcode;
using UnityEngine;
using Utils.Debug;

[HelpURL(StringConsts.DocumentationLink + "NT-A-83")]
public class PlayerSpawner : NetworkBehaviour
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(PlayerSpawner);

    #endregion

    [SerializeField] private GameObject playerPrefab;
    [SerializeField] private Transform spawnPointsRoot;
    [SerializeField] private Transform debugSpawnPointsRoot;
    [SerializeField] private GameObject spawnDeathCameraVFX;
    [SerializeField] private GameObject startRoomAnimation;
    [SerializeField] private Transform teleportSpawnPoint;

    [Header("Spawn Settings")]
    [SerializeField] private float initialPlayerSpawnDelay = 2f;
    [SerializeField] private float playerAuthorityGrantDelay = 1f;

    [Header("Teleport Settings")]
    [SerializeField] private float delayBetweenStrandedTeleports = 2f;
    [SerializeField] private float teleportVFXDuration = 5f;
    [SerializeField] private float startRoomAnimDelayBeforeTeleport = 2.5f;
    [SerializeField] private Vector3 teleportPlayerRotationOffset = new(0f, 50f, 0f);

    public Transform GetSpawnPoint(ulong clientId)
    {
        var positionsRoot = GameConfig.Instance.DebugSettings.StartInElevator ? debugSpawnPointsRoot : spawnPointsRoot;
        return positionsRoot.GetChild((int)clientId % positionsRoot.childCount);
    }

    private void Awake()
    {
        MessageType.AddReceiver<GameStartedMessage>(OnGameStarted);
        MessageType<GameStateTracker.GameState>.AddReceiver<GameStateChangedMessage>(OnGameStateUpdate);
    }

    public override void OnDestroy()
    {
        base.OnDestroy();
        MessageType.RemoveReceiver<GameStartedMessage>(OnGameStarted);
        MessageType<GameStateTracker.GameState>.RemoveReceiver<GameStateChangedMessage>(OnGameStateUpdate);
    }

    private void OnGameStarted()
    {
        if (!IsServer)
            return;

        StartCoroutine(SpawnPlayerObjects());
    }

    private IEnumerator SpawnPlayerObjects()
    {
        yield return new WaitForSeconds(initialPlayerSpawnDelay);
        var players = new List<NetworkObject>();
        foreach (var clientId in NetworkManager.Singleton.ConnectedClientsIds)
        {
            var spawnPoint = GetSpawnPoint(clientId);
            var spawnPosition = spawnPoint.position;

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Spawn Player[{clientId}] at [{spawnPosition}]");

            var playerGameObject = Instantiate(playerPrefab, spawnPosition, spawnPoint.rotation);
            var playerNetworkObject = playerGameObject.GetComponent<NetworkObject>();
            playerNetworkObject.SpawnAsPlayerObject(clientId, true);

            players.Add(playerNetworkObject);
        }

        yield return new WaitForSeconds(playerAuthorityGrantDelay);

        for (var i = 0; i < players.Count; i++)
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Grant Player[{i}] Client authority");

            var playerNetworkTransform = players[i].GetComponent<DynamicNetworkTransform>();
            playerNetworkTransform.GrantClientAuthority();

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Player[{i}] is ClientAuthoritative [{!playerNetworkTransform.IsServerAuthoritative()}]");
        }

#if UNITY_EDITOR
        if (GameConfig.Instance.DebugSettings.TravelImmediately)
        {
            var travelButton = FindFirstObjectByType<TravelButton>();
            travelButton.SimulateInteraction();
        }
#endif
    }

    private void OnGameStateUpdate(GameStateTracker.GameState newGameState)
    {
        if (newGameState == GameStateTracker.GameState.TravellingToStartRoom && IsServer)
            StartCoroutine(TeleportStrandedPlayersSequentiallyServerSide());
    }

    private IEnumerator TeleportStrandedPlayersSequentiallyServerSide()
    {
        foreach (var clientId in NetworkManager.Singleton.ConnectedClientsIds)
        {
            var player = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
            var playerHeader = player.GetComponent<PlayerHeader>();

            if (playerHeader != null && !playerHeader.IsInElevator && !playerHeader.IsInStartRoom)
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Activating VFX for stranded player [{clientId}]");

                ActivateVFXClientRpc(RpcTarget.Single(clientId, RpcTargetUse.Temp));
            }
        }

        foreach (var clientId in NetworkManager.Singleton.ConnectedClientsIds)
        {
            var player = NetworkManager.Singleton.ConnectedClients[clientId].PlayerObject;
            var playerHeader = player.GetComponent<PlayerHeader>();

            if (playerHeader != null && !playerHeader.IsInElevator && !playerHeader.IsInStartRoom)
            {
                CapsuleAnimationClientRpc();
                TeleportPlayerToStartRoomClientRpc(RpcTarget.Single(clientId, RpcTargetUse.Temp));

                StrandedPlayerReturnedToStartRoom.Send<StrandedPlayerReturnedToStartRoom>(clientId, this);

                yield return new WaitForSeconds(delayBetweenStrandedTeleports);
            }
        }
    }

    [Rpc(SendTo.SpecifiedInParams)]
    private void ActivateVFXClientRpc(RpcParams rpcParams)
    {
        if (spawnDeathCameraVFX != null)
            spawnDeathCameraVFX.SetActive(true);

        UIManager.Instance.OpenWindow(WindowType.Fade);
    }

    [Rpc(SendTo.SpecifiedInParams)]
    private void TeleportPlayerToStartRoomClientRpc(RpcParams rpcParams)
    {
        StartCoroutine(TeleportPlayerToStartRoomCoroutineClientSide());
    }

    [ClientRpc]
    private void CapsuleAnimationClientRpc()
    {
        StartCoroutine(CapsuleAnimationCoroutine());
    }

    private IEnumerator CapsuleAnimationCoroutine()
    {
        yield return new WaitForSeconds(teleportVFXDuration - startRoomAnimDelayBeforeTeleport);
        CapsuleAnimation();
    }

    private IEnumerator TeleportPlayerToStartRoomCoroutineClientSide()
    {
        yield return new WaitForSeconds(teleportVFXDuration);

        PlayerHeader.LocalOwnerPlayerHeader.InteractAction.ForceDropEverything();

        var targetSpawnPoint = teleportSpawnPoint == null ? GetSpawnPoint(NetworkManager.Singleton.LocalClientId) : teleportSpawnPoint;
        var spawnPosition = targetSpawnPoint.position;
        var spawnRotation = targetSpawnPoint.rotation * Quaternion.Euler(teleportPlayerRotationOffset);
        PlayerHeader.LocalOwnerPlayerHeader.CharacterMotor.SetPositionAndRotation(spawnPosition, spawnRotation);

        if (spawnDeathCameraVFX != null)
        {
            spawnDeathCameraVFX.SetActive(false);
            UIManager.Instance.OpenWindow(WindowType.Fade);
        }
    }

    private void CapsuleAnimation()
    {
        if (startRoomAnimation != null)
            if (startRoomAnimation.TryGetComponent<Animator>(out var animator))
                animator.SetTrigger("IsTriggerCapsule");
    }

    public void RespawnPlayerServerSide(ulong targetClientId)
    {
        StartCoroutine(RespawnCoroutine(targetClientId));
    }

    private IEnumerator RespawnCoroutine(ulong targetClientId)
    {
        ActivateVFXClientRpc(RpcTarget.Single(targetClientId, RpcTargetUse.Temp));

        yield return new WaitForSecondsRealtime(2f);

        CapsuleAnimationClientRpc();
        TeleportPlayerToStartRoomClientRpc(RpcTarget.Single(targetClientId, RpcTargetUse.Temp));
    }
}