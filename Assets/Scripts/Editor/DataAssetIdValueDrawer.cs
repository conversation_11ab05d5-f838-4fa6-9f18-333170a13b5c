using DataDictionaries;
using Sirenix.OdinInspector.Editor;
using UnityEditor;
using UnityEngine;

public class DataAssetIdValueDrawer : OdinValueDrawer<DataAssetId>
{
    protected override void DrawPropertyLayout(GUIContent label)
    {
        var value = ValueEntry.SmartValue;
        
        GUILayout.Space(5);
        
        GUILayout.BeginHorizontal();
        GUI.enabled = false;
        {
            GUILayout.Space(15);
            
            var originalLabelWidth = EditorGUIUtility.labelWidth;
            
            EditorGUIUtility.labelWidth = 40;
            value.assetTypeId = (byte)EditorGUILayout.IntField("Type", value.assetTypeId, GUILayout.Width(100));
            
            GUILayout.Space(10);
            
            EditorGUIUtility.labelWidth = 20;
            value.id = (ushort)EditorGUILayout.IntField("Id", value.id, GUILayout.Width(80));
            
            EditorGUIUtility.labelWidth = originalLabelWidth;
        }
        GUI.enabled = true;
        GUILayout.EndHorizontal();

        ValueEntry.SmartValue = value;
    }
}
