using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Consts;
using UnityCodeGen;
using Utils.Extensions;

[Generator]
public class SettingsManagerGenerator : ICodeGenerator
{
    public void Execute(GeneratorContext context)
    {
        var settingsManagerType = typeof(SettingsManager);

        var privateFields = settingsManagerType.GetFields(BindingFlags.NonPublic | BindingFlags.Instance).Where(f => f.FieldType == typeof(int) || f.FieldType == typeof(bool)).ToList();

        var code =
$@"// <auto-generated/>
using UnityEngine;

partial class {settingsManagerType.Name}
{{
    public {settingsManagerType.Name}()
    {{
        SettingsData = Resources.Load<SettingsManagerData>(nameof(SettingsManagerData));

{GetAllPropertiesInit(privateFields)}
    }}

{GetAllPropertiesCode(privateFields)}
}}";

        context.OverrideFolderPath("Assets/Scripts");
        context.AddCode("SettingsManager.Generated.cs", code);
    }

    private string GetAllPropertiesInit(List<FieldInfo> fields)
    {
        var result = string.Empty;

        foreach (var fieldInfo in fields)
        {
            var propertyName = GetPropertyNameFromFieldName(fieldInfo.Name);
            var typeName = StringConsts.TypeNames[fieldInfo.FieldType];
            var isBool = fieldInfo.FieldType == typeof(bool);
            var boolConversionTo = isBool ? ".ToInt()" : "";
            var boolConversionFrom = isBool ? ".FromInt()" : "";

            //if (!PlayerPrefs.HasKey(nameof(MouseSensitivityX))) PlayerPrefs.SetFloat(nameof(MouseSensitivityX), _mouseSensitivityX);
            result += $"        if (!PlayerPrefs.HasKey(nameof({propertyName}))) PlayerPrefs.Set{typeName}(nameof({propertyName}), {fieldInfo.Name}{boolConversionTo});\n";

            //JoystickSensitivityX = PlayerPrefs.GetFloat(nameof(JoystickSensitivityX), _joystickSensitivityX);
            result += $"        {propertyName} = PlayerPrefs.Get{typeName}(nameof({propertyName}), {fieldInfo.Name}{boolConversionTo}){boolConversionFrom};";

            if (fieldInfo != fields.Last())
                result += "\n";
        }

        return result;
    }

    private string GetAllPropertiesCode(List<FieldInfo> fields)
    {
        var result = string.Empty;

        foreach (var field in fields)
        {
            result += GetFieldPropertyCode(field);

            if (field != fields.Last())
                result += "\n\n";
        }

        return result;
    }

    private string GetFieldPropertyCode(FieldInfo fieldInfo)
    {
        var propertyName = GetPropertyNameFromFieldName(fieldInfo.Name);
        var typeName = StringConsts.TypeNames[fieldInfo.FieldType];
        var boolConversion = fieldInfo.FieldType == typeof(bool) ? ".ToInt()" : "";

        var rawToCleanAssignment = "";
        var cleanProperty = "";

        var isRaw = propertyName.Contains("Raw");
        if (isRaw)
        {
            var propertyNameClean = propertyName.Remove(propertyName.Length - 3, 3);

            rawToCleanAssignment = $"\n            {propertyNameClean} = SettingsData.{propertyNameClean}ConversionCurve.Evaluate(value);";
            cleanProperty = $"\n\n    public float {propertyNameClean} {{ get; private set; }}";
        }

        return $@"    public {fieldInfo.FieldType} {propertyName}
    {{
        get => {fieldInfo.Name};
        set
        {{
            {fieldInfo.Name} = value;{rawToCleanAssignment}
            PlayerPrefs.Set{typeName}(nameof({propertyName}), value{boolConversion}); 
        }}
    }}{cleanProperty}";
    }

    private string GetPropertyNameFromFieldName(string fieldName)
    {
        return fieldName.RemoveUnderscore().UpperFirstLetter();
    }
}
