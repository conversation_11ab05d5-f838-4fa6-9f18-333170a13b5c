using System.Collections.Generic;
using System.Linq;
using Consts;
using Cysharp.Threading.Tasks;
using EventGeneration;
using MalteHusung.GlobalMessage;
using Unity.Netcode;
using UnityEngine;
using Utils.Debug;
using Utils.Extensions;
using static GameStateTracker;
using UnityEngine.SceneManagement;


#if UNITY_EDITOR
using UnityEditor;
#endif

public partial class IterativeSceneLoader : NetworkBehaviour
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(IterativeSceneLoader);

    #endregion

    #region Settings

    [SerializeField] private List<GameObject> slotsContainers = new();
    [SerializeField] private List<GameObject> events = new();
    [SerializeField] private bool skipAutosnap;

    [SerializeField] private float autoSnapDelay = 1f;

    #endregion

    #region Privates

    private readonly HashSet<ulong> _generatorsDoneClientsIds = new(NumericConsts.MaxPlayers);
    private readonly NetworkVariable<bool> _generatorsDone = new(false);
    private NetworkVariable<ulong> _spawnedEventNetworkId = new();
    private NetworkObject _spawnedEvent;
    private readonly NetworkVariable<int> seed = new();
    private bool _isSceneLoaded = false;
    private readonly List<Generator> _generators = new();
    private DebugSettings DebugSettings => GameConfig.Instance.DebugSettings;

    #endregion

    #region Publics

    public static IterativeSceneLoader Instance { get; private set; }

    public bool IsGenerationCompleted
    {
        get
        {
            foreach (var generator in _generators)
            {
                if (!generator.IsGenerationCompleted)
                    return false;
            }

            return true;
        }
    }

    #endregion

    private void OnEnable()
    {
        _isSceneLoaded = false;
        NetworkManager.Singleton.SceneManager.OnSceneEvent += OnSceneEvent;
        MessageType<GameState>.AddReceiver<GameStateChangedMessage>(OnGameStateUpdate);
    }

    private void OnSceneEvent(SceneEvent sceneEvent)
    {
        if (sceneEvent.SceneName == gameObject.scene.name &&
            sceneEvent.SceneEventType == SceneEventType.LoadComplete)
        {
            _isSceneLoaded = true;
        }
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();
        Instance = this;
        if (IsServer)
            seed.Value = DebugSettings.UseOverrideSeed ? DebugSettings.OverrideSeed : Random.Range(int.MinValue, int.MaxValue);
        FullLevelSpawnProcedure().Forget();

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"SEED: {seed.Value}");
    }

    public override void OnNetworkDespawn()
    {
        base.OnNetworkDespawn();

        if (Instance == this)
            Instance = null;
    }

    // AC context
    private async UniTaskVoid FullLevelSpawnProcedure()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Client]: Start full level spawn procedure");

        while (!_isSceneLoaded || !InSceneNetworkBehaviourManager.Instance.AreAllClientsFullySpawned)
        {
            await UniTask.WaitForFixedUpdate();
        }

        if (GameStateTracker.Instance.CurrentState != GameState.TravellingToLocation)
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                DebugMessenger.Message($"Something went really wrong");

            return;
        }

        if (IsServer)
        {
            var randomEvent = events.Random(new System.Random(seed.Value));
            if (randomEvent != null)
                _spawnedEventNetworkId.Value = Instantiate(randomEvent).NetworkSpawn().NetworkObjectId;
        }

        if (_spawnedEventNetworkId.Value != 0)
        {
            while (!NetworkManager.Singleton.SpawnManager.SpawnedObjects.TryGetValue(_spawnedEventNetworkId.Value, out _spawnedEvent))
            {
                await UniTask.WaitForFixedUpdate();
            }

            _spawnedEvent.GetComponentsInChildren(_generators);
        }

        await PerformEventGeneration();

        while (!_generatorsDone.Value)
        {
            await UniTask.WaitForFixedUpdate();
        }

        if (!skipAutosnap)
        {
            if (IsServer)
            {
                await UniTask.WaitForSeconds(autoSnapDelay);
                await PerformAutosnaps();
            }
        }

        SceneManager.SetActiveScene(gameObject.scene);

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Client]: Full level spawn procedure done, notify Server");

        MultiplayerManager.Instance.ScenePostProcessedServerRPC(NetworkManager.LocalClientId);
    }

    #region Run Generators

    // AC context
    private async UniTask PerformEventGeneration()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Client]: Run generators");
#if UNITY_EDITOR
        if (DebugSettings.PauseOnGenerationStart)
            EditorApplication.isPaused = true;
#endif
        for (var i = 0; i < _generators.Count; i++)
        {
            _generators[i].StartGeneration(seed.Value + i);
            Debug.Log($"Generator {i} started generating");

            while (!_generators[i].IsGenerationCompleted)
                await UniTask.WaitForFixedUpdate();

            Debug.Log($"Generator {i} completed generating");
        }

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Client]: Generators done");

        GenerationCompletedServerRPC(NetworkManager.LocalClientId);

#if UNITY_EDITOR
        if (DebugSettings.PauseOnGenerationEnd)
            EditorApplication.isPaused = true;
#endif
    }

    [ServerRpc(RequireOwnership = false)]
    private void GenerationCompletedServerRPC(ulong clientID)
    {
        InSceneNetworkBehaviourManager.Instance.ConfirmationRoutine(_generatorsDoneClientsIds, clientID, _generatorsDone, "Generators done");
    }

    #endregion

    #region Autosnapping

    // SRV context
    private async UniTask PerformAutosnaps()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Autosnapping");

        var maxSnapsPerFrame = 5;
        var snapsLeft = maxSnapsPerFrame;

        // snap slots
        foreach (var slotContainer in slotsContainers)
        {
            var slots = slotContainer.GetComponentsInChildren<ItemSlot>(true);
            foreach (var slot in slots)
            {
                if (--snapsLeft <= 0)
                {
                    snapsLeft = maxSnapsPerFrame;
                    await UniTask.WaitForEndOfFrame();
                }

                slot.AutoSnap();
            }
        }

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"[Server]: Autosnapping complete");
    }

    #endregion

    #region Network Confirmation

    private void ResetNetworkConfirmationVariables()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Reset Network Confirmation Variables");

        InSceneNetworkBehaviourManager.Instance.ResetConfirmationPair(_generatorsDoneClientsIds, _generatorsDone);
    }

    private void OnGameStateUpdate(GameStateTracker.GameState newGameState)
    {
        if (IsServer && newGameState == GameStateTracker.GameState.LocationGameplay)
            ResetNetworkConfirmationVariables();
    }

    #endregion

    private void OnDisable()
    {
        DecalsManager.Instance.ClearDecals();

        MessageType<GameState>.RemoveReceiver<GameStateChangedMessage>(OnGameStateUpdate);

        if (IsServer)
        {
            if (_spawnedEvent != null)
            {
                _spawnedEvent.Despawn();
                _spawnedEventNetworkId.Value = default;
            }
        }

        NetworkManager.Singleton.SceneManager.OnSceneEvent -= OnSceneEvent;
    }

    public override void OnDestroy()
    {
        base.OnDestroy();

        // meant to detect scene unloading outside of normal procedure,
        // i.e. connection to server was lost, clients have to clean everything themselves
        if (_spawnedEventNetworkId.Value != default)
            Destroy(_spawnedEvent.gameObject);
    }
}