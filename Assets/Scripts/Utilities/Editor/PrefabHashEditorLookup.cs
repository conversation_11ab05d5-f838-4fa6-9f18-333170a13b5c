using UnityEditor;
using UnityEngine;
using Unity.Netcode;
using System.IO;

public class PrefabHashEditorLookup : EditorWindow
{
    private ulong targetHash;
    private GameObject foundPrefab;
    private string statusMessage;

    [MenuItem("Tools/Netcode/Find Prefab by Hash")]
    public static void ShowWindow()
    {
        GetWindow<PrefabHashEditorLookup>("Find Prefab by Hash");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("Find Prefab by NetworkObject Hash", EditorStyles.boldLabel);

        targetHash = (ulong)EditorGUILayout.LongField("Target Hash (ULong)", (long)targetHash);

        if (GUILayout.Button("Find Prefab"))
        {
            FindPrefabByHash(targetHash);
        }

        if (foundPrefab != null)
        {
            EditorGUILayout.ObjectField("Found Prefab:", foundPrefab, typeof(GameObject), false);
        }
        else if (!string.IsNullOrEmpty(statusMessage))
        {
            EditorGUILayout.HelpBox(statusMessage, MessageType.Info);
        }
    }

    private void FindPrefabByHash(ulong hash)
    {
        var allPrefabGuids = AssetDatabase.FindAssets("t:GameObject");
        foreach (var guid in allPrefabGuids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var asset = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);

            if (asset == null || !PrefabUtility.IsPartOfPrefabAsset(asset)) continue;
            var netObj = asset.GetComponent<NetworkObject>();
            if (netObj == null) continue;
            if (netObj.PrefabIdHash != hash) continue;
            foundPrefab = asset;
            statusMessage = $"Префаб '{foundPrefab.name}' найден напрямую из NetworkObject хеша префаба.";
        }
    }