//#define ShowValue
//#define ShowRawValue

using System;
using UnityEditor;
using UnityEngine;

[CustomPropertyDrawer(typeof(SerializedTimeSpan))]
public class TimeSpanPropertyDrawer : PropertyDrawer
{
    private const int FieldWidth = 25;
    private const int LetterWidth = 15;

    private const int FieldLabelSpacing = 1;
    private const int ComponentsSpacing = 3;

    public override void OnGUI(Rect guiData, SerializedProperty property, GUIContent label)
    {
        var ticksProperty = property.FindPropertyRelative("Ticks");

        var halfWidth = guiData.width / 2;

        EditorGUI.BeginProperty(guiData, label, property);

        var currentSpan = new TimeSpan(ticksProperty.longValue);
        int days = currentSpan.Days, hours = currentSpan.Hours, minutes = currentSpan.Minutes, seconds = currentSpan.Seconds;

        // name
        EditorGUI.LabelField(new Rect(guiData.x, guiData.y, halfWidth, guiData.height), property.displayName);

        var previousIndentLevel = EditorGUI.indentLevel;
        EditorGUI.indentLevel = 0;

        // d h m s
        var xPosition = guiData.x + halfWidth;
        var daysField = EditorGUI.IntField(new Rect(xPosition, guiData.y, FieldWidth, guiData.height), days);
        xPosition += FieldLabelSpacing;
        EditorGUI.LabelField(new Rect(xPosition += FieldWidth, guiData.y, LetterWidth, guiData.height), "d");
        xPosition += ComponentsSpacing;

        var hoursField = EditorGUI.IntField(new Rect(xPosition += LetterWidth, guiData.y, FieldWidth, guiData.height), hours);
        xPosition += FieldLabelSpacing;
        EditorGUI.LabelField(new Rect(xPosition += FieldWidth, guiData.y, LetterWidth, guiData.height), "h");
        xPosition += ComponentsSpacing;

        var minutesField = EditorGUI.IntField(new Rect(xPosition += LetterWidth, guiData.y, FieldWidth, guiData.height), minutes);
        xPosition += FieldLabelSpacing;
        EditorGUI.LabelField(new Rect(xPosition += FieldWidth, guiData.y, LetterWidth, guiData.height), "m");
        xPosition += ComponentsSpacing;

        var secondsField = EditorGUI.IntField(new Rect(xPosition += LetterWidth, guiData.y, FieldWidth, guiData.height), seconds);
        xPosition += FieldLabelSpacing;
        EditorGUI.LabelField(new Rect(xPosition += FieldWidth, guiData.y, LetterWidth, guiData.height), "s");

        EditorGUI.indentLevel = previousIndentLevel;

        // construct result
        var timeSpan = new TimeSpan(daysField, hoursField, minutesField, secondsField);

        // apply
        ticksProperty.serializedObject.Update();
        ticksProperty.longValue = timeSpan.Ticks;
        ticksProperty.serializedObject.ApplyModifiedProperties();

#if ShowValue
#if ShowRawValue
        EditorGUI.LabelField(new Rect(xPosition += 20, guiData.y, halfWidth, guiData.height), $"{ticksProperty.longValue} ticks");
#else
        EditorGUI.LabelField(new Rect(xPosition += 20, guiData.y, halfWidth, guiData.height), string.Format("{0:%d}d {0:%h}h {0:%m}m {0:%s\\:fff}s", timeSpan));
#endif
#endif

        EditorGUI.EndProperty();
    }
}