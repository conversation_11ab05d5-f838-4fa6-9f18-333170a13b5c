public partial class LobbyManager
{
    [System.Diagnostics.Conditional("LOG_LobbyManager")]
    public static void Log(string message)
    {
        UnityEngine.Debug.Log(message);
    }

    [System.Diagnostics.Conditional("LOG_WARNING_LobbyManager")]
    public static void LogWarning(string message)
    {
        UnityEngine.Debug.LogWarning(message);
    }

    [System.Diagnostics.Conditional("LOG_ERROR_LobbyManager")]
    public static void LogError(string message)
    {
        UnityEngine.Debug.LogError(message);
    }
}

public partial class UIShopItem
{
    [System.Diagnostics.Conditional("LOG_UIShopItem")]
    public static void Log(string message)
    {
        UnityEngine.Debug.Log(message);
    }

    [System.Diagnostics.Conditional("LOG_WARNING_UIShopItem")]
    public static void LogWarning(string message)
    {
        UnityEngine.Debug.LogWarning(message);
    }

    [System.Diagnostics.Conditional("LOG_ERROR_UIShopItem")]
    public static void LogError(string message)
    {
        UnityEngine.Debug.LogError(message);
    }
}

public partial class GameStateTracker
{
    [System.Diagnostics.Conditional("LOG_GameStateTracker")]
    public static void Log(string message)
    {
        UnityEngine.Debug.Log(message);
    }

    [System.Diagnostics.Conditional("LOG_WARNING_GameStateTracker")]
    public static void LogWarning(string message)
    {
        UnityEngine.Debug.LogWarning(message);
    }

    [System.Diagnostics.Conditional("LOG_ERROR_GameStateTracker")]
    public static void LogError(string message)
    {
        UnityEngine.Debug.LogError(message);
    }
}

public partial class IterativeSceneLoader
{
    [System.Diagnostics.Conditional("LOG_IterativeSceneLoader")]
    public static void Log(string message)
    {
        UnityEngine.Debug.Log(message);
    }

    [System.Diagnostics.Conditional("LOG_WARNING_IterativeSceneLoader")]
    public static void LogWarning(string message)
    {
        UnityEngine.Debug.LogWarning(message);
    }

    [System.Diagnostics.Conditional("LOG_ERROR_IterativeSceneLoader")]
    public static void LogError(string message)
    {
        UnityEngine.Debug.LogError(message);
    }
}

public partial class SessionStatsManager
{
    [System.Diagnostics.Conditional("LOG_SessionStatsManager")]
    public static void Log(string message)
    {
        UnityEngine.Debug.Log(message);
    }

    [System.Diagnostics.Conditional("LOG_WARNING_SessionStatsManager")]
    public static void LogWarning(string message)
    {
        UnityEngine.Debug.LogWarning(message);
    }

    [System.Diagnostics.Conditional("LOG_ERROR_SessionStatsManager")]
    public static void LogError(string message)
    {
        UnityEngine.Debug.LogError(message);
    }
}

public partial class CountdownTimer
{
    [System.Diagnostics.Conditional("LOG_CountdownTimer")]
    public static void Log(string message)
    {
        UnityEngine.Debug.Log(message);
    }

    [System.Diagnostics.Conditional("LOG_WARNING_CountdownTimer")]
    public static void LogWarning(string message)
    {
        UnityEngine.Debug.LogWarning(message);
    }

    [System.Diagnostics.Conditional("LOG_ERROR_CountdownTimer")]
    public static void LogError(string message)
    {
        UnityEngine.Debug.LogError(message);
    }
}

namespace Utils.Core
{
    public partial class GameInitializer
    {
        [System.Diagnostics.Conditional("LOG_GameInitializer")]
        public static void Log(string message)
        {
            UnityEngine.Debug.Log(message);
        }

        [System.Diagnostics.Conditional("LOG_WARNING_GameInitializer")]
        public static void LogWarning(string message)
        {
            UnityEngine.Debug.LogWarning(message);
        }

        [System.Diagnostics.Conditional("LOG_ERROR_GameInitializer")]
        public static void LogError(string message)
        {
            UnityEngine.Debug.LogError(message);
        }
    }
}

