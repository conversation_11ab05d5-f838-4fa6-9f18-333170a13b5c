using System;
using UnityEngine;

[ExecuteAlways, DefaultExecutionOrder(-100)]
public class TransformMirror : MonoBehaviour
{
    [SerializeField] private ReferenceMode referenceMode;
    [SerializeField] private MirrorMode mirrorMode;

    public Transform Source { get => source; set => source = value; }
    [SerializeField] private Transform source;

    public Transform Target { get => target; set => target = value; }
    [SerializeField] private Transform target;

    private void Update()
    {
        MirrorTransforms(transform);
    }

    private void MirrorTransforms(Transform thisTransform)
    {
        if (mirrorMode == MirrorMode.None)
            return;

        var sourceIsValid = source != null;
        var targetIsValid = target != null;

        if ((mirrorMode & MirrorMode.SourceToThis) != 0)
            if (sourceIsValid)
                MirrorTransforms(source, thisTransform);

        if ((mirrorMode & MirrorMode.ThisToTarget) != 0)
            if (targetIsValid)
                MirrorTransforms(thisTransform, target);

        if ((mirrorMode & MirrorMode.SourceToTarget) != 0)
            if (sourceIsValid && targetIsValid)
                MirrorTransforms(source, target);
    }

    private void MirrorTransforms(Transform source, Transform target)
    {
        if (referenceMode == ReferenceMode.Local)
        {
            source.GetLocalPositionAndRotation(out var sourceLocalPosition, out var sourceLocalRotation);
            target.SetLocalPositionAndRotation(sourceLocalPosition, sourceLocalRotation);
        }
        else
        {
            source.GetPositionAndRotation(out var sourcePosition, out var sourceRotation);
            target.SetPositionAndRotation(sourcePosition, sourceRotation);
        }
    }

    public enum ReferenceMode
    {
        Local,
        World
    }

    [Flags]
    /// <summary>
    /// Overwrite transfrom values from where to where
    /// </summary>
    public enum MirrorMode
    {
        None = 0,
        SourceToThis = 1 << 0,
        ThisToTarget = 1 << 1,
        SourceToTarget = 1 << 2
    }
}
