using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Consts;
using Cysharp.Threading.Tasks;
using MalteHusung.GlobalMessage;
using Unity.Netcode;
using Unity.Netcode.Transports.UTP;
using Unity.Networking.Transport.Relay;
using UnityEngine;
using UnityEngine.SceneManagement;
using Utils.Debug;

public class MultiplayerManager : NetworkBehaviorManager<IMultiplayerManager>, IMultiplayerManager
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(MultiplayerManager);

    #endregion

    protected override void SetInstance()
    {
        Instance = this;
    }

    protected override void Setup()
    {
        _playerDatas = new();
        StartCoroutine(Init());
    }

    private NetworkManager _networkManager;

    private NetworkList<PlayerData> _playerDatas;
    private readonly Dictionary<ulong, bool> _playerReadyDictionary = new();
    private ulong _clientId;

    public Action OnPlayersListChanged { get; set; }
    public Action OnReadyChanged { get; set; }

    /// <summary>
    /// The callback to invoke when a client disconnects.
    /// This callback is only ran on the server and on the local client that disconnects.
    /// In our implementation seems to only be called on client.
    /// </summary>
    public Action OnDisconnected { get; set; }

    public string PlayerName
    {
        get => _playerName;
        set
        {
            _playerName = value;
            PlayerPrefs.SetString(StringConsts.PlayerNameKey, value);

#if UNITY_EDITOR
            if (ParrelSync.ClonesManager.IsClone())
                _playerName += " Clone";
#endif
        }
    }

    public static bool IsInitialized { get; private set; }

    private string _playerName;

    private readonly HashSet<ulong> _scenePostProcessedClientsIds = new(NumericConsts.MaxPlayers);
    private NetworkVariable<bool> _scenePostProcessed = new(false);

    private IEnumerator Init()
    {
        while (NetworkManager.Singleton == null)
        {
            yield return null;
        }

        _networkManager = NetworkManager.Singleton;
        _playerDatas.OnListChanged += OnPlayersListChangedInternal;

        PlayerName = PlayerPrefs.GetString(StringConsts.PlayerNameKey, "Player");

        IsInitialized = true;
    }

    private void OnPlayersListChangedInternal(NetworkListEvent<PlayerData> changeEvent)
    {
        OnPlayersListChanged?.Invoke();
    }

    #region Player Name

    [ServerRpc(RequireOwnership = false)]
    public void SetPlayerNameServerRpc(string playerName, ServerRpcParams serverRpcParams = default)
    {
        var playerDataIndex = GetPlayerDataIndexByCliendId(serverRpcParams.Receive.SenderClientId);

        var playerData = _playerDatas[playerDataIndex];
        playerData.playerName = playerName;
        _playerDatas[playerDataIndex] = playerData;
    }

    #endregion

    /// <summary>
    /// Is invoked with button that can only be pressed by server
    /// </summary>
    public async void StartGameAsServer(bool fastStart = false, bool startInElevator = false, bool travelImmediately = false)
    {
        // just in case
        if (!IsServer)
            return;

        await LoadSceneWhenReady(StringConsts.World);

        // SceneManager.SetActiveScene(SceneManager.GetSceneByName(StringConsts.World));

        await LoadSceneWhenReady(StringConsts.StartRoom);
        
        MessageType.Send<Host_OnStartRoomLoadedMessage>(this);

        if (!fastStart)
        {
            await UnloadSceneWhenReady(SceneManager.GetSceneByName(StringConsts.CharacterSelection));

            LobbyManager.Instance.DeleteLobby();
        }

        // wait for Awake subscriptions to go through
        await UniTask.Delay(1000);

        // we know that this StartGame() is a server environment,
        // so we can freely send this RPC without layering it through serverRPC 
        OnGameStartedClientRPC();

        if (startInElevator && travelImmediately)
        {
            MessageType.Send<ElevatorButtonPressedMessage>(this);
        }
    }

    [ClientRpc]
    public void OnGameStartedClientRPC()
    {
        MessageType.Send<GameStartedMessage>(this);

        Cursor.lockState = CursorLockMode.Locked;
        Cursor.visible = false;
    }

    #region Stop/Start Host/Client

    public bool StartHost()
    {
        try
        {
            // can not really be correctly interacted with before network initialization, and sometimes even after
            if (_playerDatas.Count > 0)
                _playerDatas.Clear();
        }
        catch (Exception e)
        {
            Debug.LogException(e);
        }

        _playerReadyDictionary?.Clear();

        MessageType.Send<OnBeforeHostStartMessage>(this);
        
        _networkManager.ConnectionApprovalCallback += ConnectionApprovalCallback;
        _networkManager.OnClientConnectedCallback += Host_OnClientConnected;
        _networkManager.OnClientDisconnectCallback += Host_OnClientDisconnected;

        return _networkManager.StartHost();
    }

    public void StopHost()
    {
        _networkManager.Shutdown();
        _playerDatas.Clear();
        _playerReadyDictionary.Clear();

        _networkManager.ConnectionApprovalCallback -= ConnectionApprovalCallback;
        _networkManager.OnClientConnectedCallback -= Host_OnClientConnected;
        _networkManager.OnClientDisconnectCallback -= Host_OnClientDisconnected;

        ReturnToSinglePlayer();
    }

    public void StartClient()
    {
        _networkManager.OnClientConnectedCallback += Client_OnClientConnected;
        _networkManager.OnClientDisconnectCallback += Client_OnClientDisconnected;
        _networkManager.StartClient();
    }

    public void StopClient()
    {
        _networkManager.OnClientConnectedCallback -= Client_OnClientConnected;
        _networkManager.OnClientDisconnectCallback -= Client_OnClientDisconnected;

        // InvalidOperationException: Client is not allowed to write to this NetworkList
        // _playerDatas.Clear();
        _playerReadyDictionary.Clear();
        _networkManager.Shutdown();

        ReturnToSinglePlayer();
    }

    private void ReturnToSinglePlayer()
    {
        MessageType.Send<GameEndedMessage>(this);
        MessageType.Send<ReturnToMainMenuMessage>(this);


        Cursor.lockState = CursorLockMode.None;
        Cursor.visible = true;
    }

    #endregion

    #region Player Connection

    public bool IsPlayerIndexConnected(int playerIndex)
    {
        return playerIndex < _playerDatas.Count;
    }

    #region OnClientConnected

    private void Host_OnClientConnected(ulong clientId)
    {
        _playerDatas.Add(new PlayerData { clientId = clientId });

        _playerReadyDictionary[clientId] = false;
        SetClientStatusesClientRpc(JsonUtility.ToJson(new PlayerStatuses(_playerReadyDictionary)));

        SetPlayerNameServerRpc(PlayerName);
        
        Host_OnClientConnectedMessage.Send<Host_OnClientConnectedMessage>(clientId, this);
    }

    /// <summary>
    /// The callback to invoke once a client connects. This callback is only ran on the server and on the local client that connects.
    /// </summary>
    private void Client_OnClientConnected(ulong clientId)
    {
        _clientId = clientId;
        SetPlayerNameServerRpc(PlayerName);
    }

    #endregion

    #region OnClientDisconnected

    private void Host_OnClientDisconnected(ulong clientId)
    {
        try
        {
            for (var i = 0; i < _playerDatas.Count; i++)
            {
                if (_playerDatas[i].clientId != clientId)
                    continue;

                _playerDatas.RemoveAt(i);
            }
        }
        catch (Exception e)
        {
            Debug.LogException(e);
        }

        _playerReadyDictionary.Remove(clientId);
        SetClientStatusesClientRpc(JsonUtility.ToJson(new PlayerStatuses(_playerReadyDictionary)));
    }

    /// <summary>
    /// The callback to invoke when a client disconnects. This callback is only ran on the server and on the local client that disconnects.
    /// </summary>
    private void Client_OnClientDisconnected(ulong clientId)
    {
        // mind the order
        OnDisconnected?.Invoke();
        StopClient();
    }

    #endregion

    #endregion

    #region Scene Management

    private bool _sceneIsLoading = false;

    private void OnLoadEventCompleted(string sceneName, LoadSceneMode loadSceneMode, List<ulong> clientsCompleted, List<ulong> clientsTimedOut)
    {
        _sceneIsLoading = false;
    }

    public async UniTask LoadSceneWhenReady(string sceneName)
    {
        while (_networkManager.SceneManager == null)
            await UniTask.WaitForFixedUpdate();
        
        _networkManager.SceneManager.LoadScene(sceneName, LoadSceneMode.Additive);
        _networkManager.SceneManager.OnLoadEventCompleted += OnLoadEventCompleted;

        _sceneIsLoading = true;

        while (_sceneIsLoading)
            await UniTask.WaitForFixedUpdate();

        _networkManager.SceneManager.OnLoadEventCompleted -= OnLoadEventCompleted;

        // travelling now into gameplay room, (presumably the only place in need of post processing)
        if (GameStateTracker.Instance.CurrentState == GameStateTracker.GameState.StartRoom)
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Scene {sceneName} loaded, start post processing");

            // basically relies on IterativeSceneLoader.OnNetworkSpawn() to run the procedure and change _scenePostProcessed.Value
            // I would like to search for IterativeSceneLoader instance and skip post processing if it is not found
            // TODO
            if (sceneName.ToLower().Contains(StringConsts.Level))
                while (!_scenePostProcessed.Value)
                    await UniTask.WaitForFixedUpdate();

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Scene {sceneName} loaded and processed");

            InSceneNetworkBehaviourManager.Instance.ResetConfirmationPair(_scenePostProcessedClientsIds, _scenePostProcessed);
        }
    }

    [ServerRpc(RequireOwnership = false)]
    public void ScenePostProcessedServerRPC(ulong clientID)
    {
        InSceneNetworkBehaviourManager.Instance.ConfirmationRoutine(_scenePostProcessedClientsIds, clientID, _scenePostProcessed, "Scene post-processing");
    }

    public async UniTask UnloadSceneWhenReady(Scene scene)
    {
        while (_networkManager.SceneManager.UnloadScene(scene) == SceneEventProgressStatus.SceneEventInProgress)
        {
            await UniTask.WaitForFixedUpdate();
        }
    }

    #endregion

    #region Player Data

    public PlayerData GetPlayerData(int playerIndex)
    {
        return _playerDatas[playerIndex];
    }

    public PlayerData GetPlayerDataByClientId(ulong clientId)
    {
        foreach (var playerData in _playerDatas)
        {
            if (playerData.clientId == clientId)
                return playerData;
        }

        return default;
    }

    private int GetPlayerDataIndexByCliendId(ulong clientId)
    {
        for (var index = 0; index < _playerDatas.Count; index++)
        {
            var playerData = _playerDatas[index];
            if (playerData.clientId == clientId)
                return index;
        }

        return -1;
    }

    #endregion

    #region Ready State

    public void ToggleReady()
    {
        ToggleReadyServerRpc();
    }

    [ServerRpc(RequireOwnership = false)]
    private void ToggleReadyServerRpc(ServerRpcParams serverRpcParams = default)
    {
        var senderId = serverRpcParams.Receive.SenderClientId;
        _playerReadyDictionary.TryGetValue(senderId, out var oldValue);
        var newValue = !oldValue;
        _playerReadyDictionary[senderId] = newValue;

        SetClientReadyClientRpc(senderId, newValue);
    }

    [ClientRpc]
    private void SetClientReadyClientRpc(ulong clientId, bool isReady)
    {
        _playerReadyDictionary[clientId] = isReady;
        OnReadyChanged?.Invoke();
    }

    [ClientRpc]
    private void SetClientStatusesClientRpc(string clientsStatuses)
    {
        if (!IsHost)
        {
            var clientsData = JsonUtility.FromJson<PlayerStatuses>(clientsStatuses);

            if (clientsData != null && clientsData.Clients.Length > 0)
                _playerReadyDictionary.Clear();

            for (int i = 0; i < clientsData.Clients.Length; i++)
                _playerReadyDictionary[clientsData.Clients[i]] = clientsData.Statuses[i];
        }

        OnReadyChanged?.Invoke();
    }

    public bool AreAllClientsReady()
    {
        return _playerReadyDictionary.Values.All(isReady => isReady);
    }

    public bool IsPlayerReadyByIndex(int playerIndex)
    {
        return IsPlayerReadyByClientId(GetPlayerData(playerIndex).clientId);
    }

    public bool IsPlayerReadyByClientId(ulong clientId)
    {
        _playerReadyDictionary.TryGetValue(clientId, out var isReady);

        return isReady;
    }

    #endregion

    #region Networking

    private void ConnectionApprovalCallback(
        NetworkManager.ConnectionApprovalRequest connectionApprovalRequest,
        NetworkManager.ConnectionApprovalResponse connectionApprovalResponse)
    {
        connectionApprovalResponse.Approved = true;
    }

    public void SetRelayServerData(RelayServerData data)
    {
        _networkManager.GetComponent<UnityTransport>().SetRelayServerData(data);
    }

    #endregion
}

[System.Serializable]
public class PlayerStatuses
{
    public ulong[] Clients;
    public bool[] Statuses;

    public PlayerStatuses(Dictionary<ulong, bool> input)
    {
        Clients = input.Keys.ToArray();
        Statuses = input.Values.ToArray();
    }
}