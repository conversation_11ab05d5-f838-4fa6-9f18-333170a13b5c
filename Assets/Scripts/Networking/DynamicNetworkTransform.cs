using KinematicCharacterController;
using Sirenix.OdinInspector;
using Unity.Netcode;
using Unity.Netcode.Components;
using UnityEngine;
using Utils.Debug;

/// <summary>
/// Is not dynamic, in the end. Is just a different <see cref="ClientNetworkTransform"/>.
/// </summary>
[DisallowMultipleComponent]
public class DynamicNetworkTransform : NetworkTransform
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(DynamicNetworkTransform);

    #endregion

    private bool IsClientAuthoritative
    {
        get => isClientAuthoritative.Value;
        set
        {
            if (IsServer)
            {
                isClientAuthoritative.Value = value;
                SynchronizeAuthoritativeStateClientRPC();
            }
        }
    }
    private NetworkVariable<bool> isClientAuthoritative = new(false);

    [Button]
    public void GrantClientAuthority()
    {
        IsClientAuthoritative = true;
    }

    [Button]
    public void RevokeClientAuthority()
    {
        IsClientAuthoritative = false;
    }

    // does not work properly, Initialize() is not enough to convince everyone in authority change
    // so it's basically just a ClientNetworkTransform
    protected override bool OnIsServerAuthoritative()
    {
        return false; //!IsClientAuthoritative;
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        // this is the most important part, other things do nothing
        GetComponent<KinematicCharacterMotor>().SyncCurrentTransform();

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
        {
            DebugMessenger.Message($"Player[{OwnerClientId}] spawn position [{transform.position}]");
            DebugMessenger.Message($"Player[{OwnerClientId}] is ClientAuthoritative [{!IsServerAuthoritative()}]");
        }
    }

    public override void OnNetworkDespawn()
    {
        base.OnNetworkDespawn();

        try
        {
            IsClientAuthoritative = false;
        }
        catch (System.Exception nullReference)
        {
            // NetworkManager.BehaviourUpdater is already null, but it can not be reached for check
            Debug.LogException(nullReference);
        }
    }

    [ClientRpc]
    private void SynchronizeAuthoritativeStateClientRPC()
    {
        // reinitialize NetworkTransform with new OnIsServerAuthoritative() value
        Initialize();
    }

    public override void OnUpdate()
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Player[{OwnerClientId}] position before update [{transform.position}]");

        base.OnUpdate();

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Player[{OwnerClientId}] position after update [{transform.position}]");
    }
}