using Unity.Netcode;
/// <summary>
/// RPC documentation
/// </summary>
public class MultiplayerCheatSheet
{
    /// <summary>
    /// Normal method within workflow
    /// </summary>
    public void DoSomething()
    {
        var usualChecks = true;

        if (usualChecks)
        {
            DoSomethingServerRPC();

            // Do something that is not needed to be synced to other "mirrors" of 'this' on other clients
        }
    }

    /// <summary>
    /// Do something that only server can do:
    /// Spawn/Despawn things, move server-authoritative transfroms
    /// </summary>
    [ServerRpc]
    private void DoSomethingServerRPC()
    {
        var isServerOkayWithThat = true;

        if (isServerOkayWithThat)
        {
            // Do server specific stuff here, if any

            // Propagate to Clients
            DoSomethingClientRPC();
        }
    }

    /// <summary>
    /// Continue with normal logic that we want to be performed on all instances of the **same** <see cref="NetworkBehaviour"/> on all clients
    /// </summary>
    [ClientRpc]
    private void DoSomethingClientRPC()
    {
        // Do what you'd normally do after 'usualChecks' - the Something
        // unless it is not something that is needed to be sent to "mirrors" of 'this' class on other clients
    }
}