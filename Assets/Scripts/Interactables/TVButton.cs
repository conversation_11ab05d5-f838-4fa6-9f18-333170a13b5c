using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;
using UnityEngine.Video;

public class TVButton : GenericInteractable
{
    [SerializeField] private VideoPlayer videoPlayer;
    [ShowInInspector, ReadOnly] private double _cachedVideoPlayerTime;

    public override bool RemoteInteractionAllowed => false;

    public override void InteractServerSide(ulong clientID)
    {
        base.InteractServerSide(clientID);
        SyncVideoPlayerStateClientRpc(videoPlayer.time, !videoPlayer.isPlaying);
    }

    private void OnDisable()
    {
        RenderTexture.active = videoPlayer.targetTexture;
        GL.Clear(true, true, Color.clear);
        RenderTexture.active = null;
    }

    [ClientRpc]
    private void SyncVideoPlayerStateClientRpc(double time, bool isPlaying)
    {
        videoPlayer.time = time;
        if (isPlaying && !videoPlayer.isPlaying)
        {
            videoPlayer.time = _cachedVideoPlayerTime;
            videoPlayer.Play();
        }
            
        
        if (!isPlaying && videoPlayer.isPlaying)
            videoPlayer.Pause();
    }

    private void Update()
    {
        if (videoPlayer.isPlaying && videoPlayer.time > 0)
            _cachedVideoPlayerTime = videoPlayer.time;
    }
}
