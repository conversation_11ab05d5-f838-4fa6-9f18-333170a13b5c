using Consts;
using Unity.Netcode;
using UnityEngine;

[HelpURL(StringConsts.DocumentationLink + "NT-A-94")]
public class GarbageBagDispenser : GenericInteractable
{
    [SerializeField] private Transform garbageBagSpawnPoint;
    [SerializeField] private GarbageBagRoll garbageBagRoll;

    private Animator _animator;

    private const string OneHandAnimationName = "OneHandAnimation";
    private readonly int OneHandAnimationHash = Animator.StringToHash(OneHandAnimationName);

    public override bool RemoteInteractionAllowed => false;

    private void Awake()
    {
        _animator = GetComponent<Animator>();
    }

    public override bool RequestInteraction(InteractAction interactor)
    {
        return !_animator.GetCurrentAnimatorStateInfo(0).IsName(OneHandAnimationName);
    }

    public override void InteractClientSide(IGrabbable itemInHands, bool isOwner, Vector3 raycastPoint, Vector3 headDirection, ulong clientId, InteractAction interactor)
    {
        if (IsServer)
        {
            var localGarbageBagRoll = Instantiate(garbageBagRoll, garbageBagSpawnPoint.position, garbageBagSpawnPoint.rotation);
            localGarbageBagRoll.GetComponent<NetworkObject>().Spawn();
        }

        _animator.Play(OneHandAnimationHash);
    }
}
