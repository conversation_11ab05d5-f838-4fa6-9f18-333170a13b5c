using System.Collections;
using Unity.Netcode;
using UnityEngine;

public class CooldownEventButton : EventButton
{
    [field: SerializeField]
    protected float CooldownDuration { get; private set; }

    private NetworkVariable<bool> _isOnCooldown = new(false);

    public override bool RequestInteraction(InteractAction interactor)
    {
        return !_isOnCooldown.Value && base.RequestInteraction(interactor);
    }

    public override void InteractOriginalClientSide(ulong clientID, InteractAction interactor = null)
    {
        base.InteractOriginalClientSide(clientID);
        StartCooldownServerRPC();
    }

    [ServerRpc(RequireOwnership = false)]
    private void StartCooldownServerRPC()
    {
        if (_isOnCooldown.Value)
            return;

        _isOnCooldown.Value = true;
        StartCoroutine(CooldownCoroutine());
    }

    private IEnumerator CooldownCoroutine()
    {
        yield return new WaitForSeconds(CooldownDuration);
        _isOnCooldown.Value = false;
    }
}