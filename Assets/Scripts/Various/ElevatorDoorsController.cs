using MalteHusung.GlobalMessage;
using Sirenix.OdinInspector;
using UnityEngine;

// TODO rewrite / delete class
public class ElevatorDoorsController : MonoBehaviour
{
    [SerializeField] private Animator doorsAnimator;

    [SerializeField, Tooltip("True = Start Room Doors, False = Location Doors")] private bool isStartRoomDoorsController;

    private static int isOpenVariableHash = Animator.StringToHash("isOpen");
    private bool _isTravelling;

    public bool IsOpened => doorsAnimator.GetBool(isOpenVariableHash);

    private void Awake()
    {
        MessageType<GameStateTracker.GameState>.AddReceiver<GameStateChangedMessage>(OnGameStateUpdate);
    }

    private void OnGameStateUpdate(GameStateTracker.GameState newGameState)
    {
        switch (newGameState)
        {
            case GameStateTracker.GameState.TravellingToStartRoom:
            case GameStateTracker.GameState.TravellingToLocation:
                _isTravelling = true;
                break;

            case GameStateTracker.GameState.StartRoom:
            case GameStateTracker.GameState.LocationGameplay:
                _isTravelling = false;
                break;
        }

        switch (newGameState)
        {
            case GameStateTracker.GameState.DepartingFromStartRoom:
            case GameStateTracker.GameState.DepartingFromLocation:
                CloseDoors();
                break;

            case GameStateTracker.GameState.ClosingForDelivery:
                if (isStartRoomDoorsController)
                {
                    if (IsOpened)
                    {
                        CloseDoors();
                    }
                    else
                    {
                        MessageType.Send<ElevatorDoorsClosedMessage>(this);
                    }
                }
                break;
            case GameStateTracker.GameState.StartRoom:
            case GameStateTracker.GameState.ArrivingToStartRoom:
            case GameStateTracker.GameState.OpeningAfterDelivery:
                if (isStartRoomDoorsController)
                    OpenDoors();
                break;

            case GameStateTracker.GameState.ArrivingToLocation:
                if (!isStartRoomDoorsController)
                    OpenDoors();
                break;

            default:
                break;
        }
    }

    public void OpenDoors()
    {
        doorsAnimator.SetBool(isOpenVariableHash, true);
    }

    public void CloseDoors()
    {
        doorsAnimator.SetBool(isOpenVariableHash, false);
    }

    [Button]
    public void ToggleDoorsStatus()
    {
        if (_isTravelling)
            return;

        var doorStatus = doorsAnimator.GetBool(isOpenVariableHash);
        doorsAnimator.SetBool(isOpenVariableHash, !doorStatus);

        if (isStartRoomDoorsController)
        {
            // interrupt gameplay if player stayed behind
            if (GameStateTracker.Instance.CurrentState == GameStateTracker.GameState.LocationGameplay)
                MessageType.Send<TimerRedlineMessage>(this);
        }
    }

    private void OnDestroy()
    {
        MessageType<GameStateTracker.GameState>.RemoveReceiver<GameStateChangedMessage>(OnGameStateUpdate);
    }
    
    public void OnDoorsOpened()
    {
        MessageType.Send<ElevatorDoorsOpenedMessage>(this);
    }
    
    public void OnDoorsClosed()
    {
        MessageType.Send<ElevatorDoorsClosedMessage>(this);
         
    }
}
