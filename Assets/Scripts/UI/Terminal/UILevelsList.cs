using Cysharp.Text;
using TMPro;
using UI;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UI;

public class UILevelsList : MonoBehaviour
{
    [SerializeField] private GameObject levelItemPrefab;
    [SerializeField] private Transform container;
    [SerializeField] private NetworkToggleGroup networkToggleGroup;

    private ToggleGroup _toggleGroup;

    private void Awake()
    {
        _toggleGroup = container.GetComponent<ToggleGroup>();
    }
    
    private void OnEnable()
    {
        RenderLevels();
    }

    private void RenderLevels()
    {
        RenderListInUI.Render(LevelManager.Instance.Levels, container, levelItemPrefab, null, LevelSetupAction);
        LayoutRebuilder.ForceRebuildLayoutImmediate(container as RectTransform);
        networkToggleGroup.UpdateTogglesList();
    }

    private void LevelSetupAction(int index, Transform itemTransform, LevelData levelData)
    {
        var rectTransform = itemTransform as RectTransform;
        rectTransform.localPosition = Vector3.zero;

        var label = itemTransform.GetComponentInChildren<TMP_Text>();

        var toggle = itemTransform.GetComponent<CustomToggle>();
        toggle.group = _toggleGroup;

        if (toggle.Data == null)
            toggle.OnValueChanged += OnToggleValueChanged;
        
        toggle.Data = levelData;
        
        using var stringBuilder = ZString.CreateStringBuilder();
        stringBuilder.Append(levelData.LocalizedName.IsEmpty ? levelData.name : levelData.LocalizedName.GetLocalizedString());
        label.SetText(stringBuilder);
    }

    private void OnToggleValueChanged(CustomToggle toggle)
    {
        if (!toggle.isOn) return;

        var levelData = toggle.Data as LevelData;
        if (levelData != null)
            LevelManager.Instance.SelectedNextLevel = levelData;
    }
}