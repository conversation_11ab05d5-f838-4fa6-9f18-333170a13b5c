using FMODUnity;
using MalteHusung.GlobalMessage;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace UI.Terminal
{
    public class UIDeliverButton : MonoBehaviour
    {
        [SerializeField] private Button button;
        [SerializeField] private TMP_Text buttonText;
        [SerializeField] private Image buttonBackground;

        [SerializeField] private EventReference errorSound;
        [SerializeField] private Transform soundPosition;

        public bool Enabled
        {
            get => _enabled;
            set
            {
                _enabled = value;
                buttonBackground.color = value ? Color.green : Color.red;
                buttonText.color = value ? Color.green : Color.red;
            }
        }

        private bool _enabled;

        private void Awake()
        {
            PlayersCountInElevatorChanged.AddReceiver<PlayersCountInElevatorChanged>(OnPlayersCountInElevatorChanged);
            MessageType.AddReceiver<ShopCartChangedMessage>(OnShopCartChanged);
            button.onClick.AddListener(OnButtonClick);
        }

        private void OnShopCartChanged()
        {
            UpdateState();
        }

        private void OnEnable()
        {
            UpdateState();
        }

        private void OnButtonClick()
        {
            if (!Enabled)
                RuntimeManager.PlayOneShot(errorSound.Guid, soundPosition.position);
        }

        private void OnPlayersCountInElevatorChanged(int playersCount)
        {
            UpdateState();
        }

        private void UpdateState()
        {
            Enabled = !Elevator.HasPlayers
                      && Shop.Instance.TotalCartItemCount > 0;
            //Debug.Log(Enabled);
        }
    }
}