using System;
using System.Collections.Generic;
using InputHandling;
using Rewired;
using Unity.Netcode;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace UI
{
    public class TerminalCursor : NetworkBehaviour, IInputListener
    {
        [SerializeField] private Image image;

        [SerializeField] private Sprite normalSprite;
        [SerializeField] private Sprite handSprite;

        [SerializeField] private float sensitivity;
        [SerializeField] private Canvas canvas;

        private Player _player;
        private RectTransform _rectTransform;
        private RectTransform _canvasRectTransform;
        private GraphicRaycaster _graphicRaycaster;
        private List<RaycastResult> _raycastResults = new(32);

        private bool _needSimulateMove;
        private bool _mouseDown;
        private bool _mouseUp;
        private Vector2 _dragOffset;
        private Vector2 _lastPosition;
        private Vector2 _lastLocalPosition;
        private Vector2 _accumulatedMovement;
        private PointerEventData _pointerEventData;
        private GameObject _draggedObject;
        private GameObject _hoveredObject;
        private GameObject _previouslyHoveredObject;

        private readonly NetworkVariable<Vector2> _targetPosition = new(default, NetworkVariableReadPermission.Everyone, NetworkVariableWritePermission.Owner);

        private void Awake()
        {
            _player = ReInput.players.GetPlayer(0);
            _rectTransform = GetComponent<RectTransform>();
            _canvasRectTransform = canvas.GetComponent<RectTransform>();
            _graphicRaycaster = canvas.GetComponent<GraphicRaycaster>();
            _pointerEventData = new PointerEventData(EventSystem.current);
            _targetPosition.OnValueChanged = TargetPositionOnValueChanged;
        }

        private void TargetPositionOnValueChanged(Vector2 previousValue, Vector2 newValue)
        {
            _needSimulateMove = true;
        }

        public bool IsInputEnabled { get; private set; }

        public void EnableInput()
        {
            _player.AddInputEventDelegate(OnMouseXMove, UpdateLoopType.Update, InputActionEventType.AxisActive, RewiredConsts.Action.TerminalCursorHorizontal);
            _player.AddInputEventDelegate(OnMouseYMove, UpdateLoopType.Update, InputActionEventType.AxisActive, RewiredConsts.Action.TerminalCursorVertical);
            _player.AddInputEventDelegate(OnMouseDown, UpdateLoopType.Update, InputActionEventType.ButtonJustPressed, RewiredConsts.Action.TerminalClick);
            _player.AddInputEventDelegate(OnMouseUp, UpdateLoopType.Update, InputActionEventType.ButtonJustReleased, RewiredConsts.Action.TerminalClick);

            IsInputEnabled = true;
        }

        public void DisableInput()
        {
            _player.RemoveInputEventDelegate(OnMouseXMove);
            _player.RemoveInputEventDelegate(OnMouseYMove);
            _player.RemoveInputEventDelegate(OnMouseDown);
            _player.RemoveInputEventDelegate(OnMouseUp);

            IsInputEnabled = false;
        }

        private void OnMouseDown(InputActionEventData eventData)
        {
            _mouseDown = true;
        }

        private void OnMouseUp(InputActionEventData eventData)
        {
            _mouseUp = true;
        }

        private void OnMouseXMove(InputActionEventData eventData)
        {
            _accumulatedMovement += new Vector2(eventData.GetAxis() * sensitivity, 0f);
        }

        private void OnMouseYMove(InputActionEventData eventData)
        {
            _accumulatedMovement += new Vector2(0f, eventData.GetAxis() * sensitivity);
        }

        private void Update()
        {
            UpdatePosition();
            _raycastResults.Clear();
            _graphicRaycaster.Raycast(_pointerEventData, _raycastResults);
            _previouslyHoveredObject = _hoveredObject;
            _hoveredObject = _raycastResults.Count > 0 ? _raycastResults[0].gameObject : null;

            if (_mouseDown)
                SimulateMouseDown();
            if (_mouseUp)
                SimulateMouseUp();
            if (_needSimulateMove)
            {
                SimulateMove();
            }

            if (_player.GetButton(RewiredConsts.Action.TerminalClick))
            {
                SimulateDrag();
            }
        }

        private void LateUpdate()
        {
            var actualMovement = new Vector2(Mathf.FloorToInt(_accumulatedMovement.x), Mathf.FloorToInt(_accumulatedMovement.y));

            if (actualMovement.magnitude > 0f)
            {
                var newPosition = _rectTransform.anchoredPosition + actualMovement;
                _targetPosition.Value = new Vector2(Mathf.Clamp(newPosition.x, 0f, _canvasRectTransform.rect.width), Mathf.Clamp(newPosition.y, -_canvasRectTransform.rect.height, 0f));
                _accumulatedMovement -= actualMovement;
            }

            _rectTransform.anchoredPosition = Vector2.Lerp(_rectTransform.anchoredPosition, _targetPosition.Value, 1f);
        }

        private void UpdatePosition()
        {
            _lastPosition = _pointerEventData.position;
            _pointerEventData.position = canvas.worldCamera.WorldToScreenPoint(transform.position);

            _pointerEventData.delta = _pointerEventData.position - _lastPosition;
        }

        private void SimulateMouseDown()
        {
            if (_hoveredObject != null)
            {
                _draggedObject = _hoveredObject;
                _pointerEventData.pointerPress = _draggedObject;
                _pointerEventData.pointerDrag = _draggedObject;
                _pointerEventData.pressPosition = _pointerEventData.position;
                _pointerEventData.pointerPressRaycast = _raycastResults[0];
                _pointerEventData.pointerCurrentRaycast = _raycastResults[0];
                _pointerEventData.dragging = false;
                _pointerEventData.useDragThreshold = true;

                ExecuteEvents.ExecuteHierarchy(_draggedObject, _pointerEventData, ExecuteEvents.pointerDownHandler);
                ExecuteEvents.ExecuteHierarchy(_draggedObject, _pointerEventData, ExecuteEvents.initializePotentialDrag);
            }

            _mouseDown = false;
        }

        private void SimulateMouseUp()
        {
            if (_draggedObject != null)
            {
                _pointerEventData.dragging = false;

                if (_pointerEventData.pointerDrag != null)
                {
                    ExecuteEvents.ExecuteHierarchy(_pointerEventData.pointerDrag, _pointerEventData, ExecuteEvents.endDragHandler);
                }

                ExecuteEvents.Execute(_draggedObject, _pointerEventData, ExecuteEvents.pointerUpHandler);

                if (_hoveredObject == _pointerEventData.pointerPress)
                {
                    ExecuteEvents.ExecuteHierarchy(_hoveredObject, _pointerEventData, ExecuteEvents.pointerClickHandler);
                }

                _draggedObject = null;
                _pointerEventData.pointerDrag = null;
                _pointerEventData.pointerPress = null;
            }

            _mouseUp = false;
        }

        private void SimulateMove()
        {
            if (_previouslyHoveredObject != _hoveredObject)
            {
                if (_previouslyHoveredObject != null)
                {
                    ExecuteEvents.ExecuteHierarchy(_previouslyHoveredObject, _pointerEventData, ExecuteEvents.pointerExitHandler);
                }

                if (_hoveredObject != null)
                {
                    ExecuteEvents.ExecuteHierarchy(_hoveredObject, _pointerEventData, ExecuteEvents.pointerEnterHandler);
                }

                var hoveredObjectName = _hoveredObject == null ? string.Empty : _hoveredObject.name;
                image.sprite = hoveredObjectName.EndsWith("_ref") ? handSprite : normalSprite;
            }

            _needSimulateMove = false;
        }

        private void SimulateDrag()
        {
            if (_draggedObject == null) return;

            if (_raycastResults.Count > 0)
                _pointerEventData.pointerPressRaycast = _raycastResults[0];
            if (!_pointerEventData.dragging)
            {
                var magnitude = _pointerEventData.delta.magnitude;
                if (magnitude > EventSystem.current.pixelDragThreshold)
                {
                    _pointerEventData.dragging = true;
                    ExecuteEvents.ExecuteHierarchy(_draggedObject, _pointerEventData, ExecuteEvents.beginDragHandler);
                }
            }

            // Если драг активен, отправляем событие
            if (_pointerEventData.dragging)
            {
                ExecuteEvents.ExecuteHierarchy(_draggedObject, _pointerEventData, ExecuteEvents.dragHandler);
            }
        }
    }
}