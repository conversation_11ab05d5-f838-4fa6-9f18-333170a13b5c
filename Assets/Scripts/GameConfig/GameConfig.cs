using System;
using System.Collections.Generic;
using Consts;
using Sirenix.OdinInspector;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

[CreateAssetMenu(menuName = StringConsts.DataAssetsFolder + "GameConfig", fileName = "GameConfig")]
public class GameConfig : SerializedScriptableObject, IBindableScriptableObject
{
    private const string MoneyGroup = "Money";
    
    [field: SerializeField] public MaterialSettings MaterialSettings { get; private set; }
    [field: SerializeField] public DecalSettings DecalSettings { get; private set; }
    
    [field: SerializeField, FoldoutGroup(MoneyGroup)] public JobItemValuesContainer JobItemValuesContainer { get; private set; }
    [field: SerializeField, FoldoutGroup(MoneyGroup)] public int StrandedPlayerPenalty { get; private set; }
    
    [field: SerializeField] public DebugSettings DebugSettings { get; private set; }

    public static GameConfig Instance { get; private set; }

    public void Bind()
    {
        Instance = this;
    }

#if UNITY_EDITOR
    [Button]
    public void Save()
    {
        EditorUtility.SetDirty(this);
        AssetDatabase.SaveAssets();
    }
#endif
}

/// <summary>
/// Part of the <see cref="GameConfig"/>
/// </summary>
[Serializable]
public class MaterialSettings
{
    [field: SerializeField] public Material SnappableItemMaterial { get; private set; }
    [field: SerializeField] public List<Material> AllSnappableItemMaterials { get; private set; }
    [field: SerializeField] public Material SnappedItemMaterial { get; private set; }
}

/// <summary>
/// Part of the <see cref="GameConfig"/>
/// </summary>
[Serializable, HelpURL(StringConsts.DocumentationLink + "NT-A-90")]
public class DecalSettings
{
    [field: SerializeField] public float DissolutionStepDuration { get; private set; }

    // parameter goes from 1 to 0, so the graph is going in reverse
    [field: SerializeField] public Easing FadeEasing { get; private set; }

    [field: SerializeField] public Material DecalUberMaterial { get; private set; }
    [field: SerializeField] public int HitsToClean { get; private set; }
    [field: SerializeField] public Material CleanCompleteBlinkMaterial { get; private set; }
    [field: SerializeField] public float CleanCompleteBlinkDuration { get; private set; }
    [field: SerializeField] public FMODUnity.EventReference CleanCompleteSFX { get; private set; }
    [field: SerializeField] public float CleanCompleteSFXDelay { get; private set; }
    [field: SerializeField] public ParticleSystem CleanCompleteVFX { get; private set; }
    [field: SerializeField] public float CleanCompleteVFXDelay { get; private set; }
    [field: SerializeField] public Texture2D DecalPalette { get; private set; }
    [field: SerializeField] public float SofteningDuration { get; private set; }
}


public enum LaunchMode
{
    Normal, FastSoloStart, ParrelSyncHostAndJoin,
}

[Serializable]
public class DebugSettings
{
    [field: SerializeField] public LaunchMode LaunchMode { get; private set; }
    [field: SerializeField] public bool StartInElevator { get; private set; }
    [field: SerializeField] public bool TravelImmediately { get; private set; }
    [field: SerializeField] public bool DisableTimer { get; private set; }
    [field: SerializeField] public bool EnableProjector { get; private set; }

    [field: SerializeField] public bool UseOverrideSeed { get; private set; }
    [field: SerializeField] public int OverrideSeed { get; private set; }
    [field: SerializeField] public bool PauseOnGenerationStart { get; private set; }
    [field: SerializeField] public bool PauseOnGenerationEnd { get; private set; }
    [field: SerializeField] public bool ClearInventoryOnStart { get; private set; }
    [field: SerializeField] public int StartCocosAmount { get; private set; }
    [field: SerializeField] public bool ZeroMinimumTravelTime { get; private set; }
    
    [field: SerializeField] public bool OverrideTimer { get; private set; }
    [field: SerializeField] public float OverrideTimerDuration { get; private set; }
}