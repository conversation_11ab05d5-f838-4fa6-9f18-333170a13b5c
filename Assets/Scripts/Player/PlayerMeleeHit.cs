using Rewired;

using UnityEngine;

public class PlayerMeleeHit : Mono<PERSON><PERSON>aviour
{
    public Camera cam;

    private Animator animator;
    private AudioSource audioSource;

    public PlayerHeader PlayerStartPoint { get; set; }

    private void Awake()
    {
        animator = GetComponentInChildren<Animator>();
        audioSource = GetComponent<AudioSource>();
    }

    private void OnEnable()
    {
        EnableInput();
    }

    private void OnDisable()
    {
        DisableInput();
    }

    private void EnableInput()
    {
        ReInput.players.GetPlayer(0).AddInputEventDelegate(Attack,
                                                           UpdateLoopType.Update,
                                                           InputActionEventType.ButtonJustPressed,
                                                           RewiredConsts.Action.Attack);
    }

    private void DisableInput()
    {
        ReInput.players.GetPlayer(0).RemoveInputEventDelegate(Attack);
    }

    // ---------- //
    // ANIMATIONS //
    // ---------- //

    public const string IDLE = "Idle";
    public const string WALK = "Walk";
    public const string ATTACK1 = "Attack 1";
    public const string ATTACK2 = "Attack 2";
    private string currentAnimationState;

    public void ChangeAnimationState(string newState)
    {
        // STOP THE SAME ANIMATION FROM INTERRUPTING WITH ITSELF //
        if (currentAnimationState == newState)
            return;

        // PLAY THE ANIMATION //
        currentAnimationState = newState;
        animator.CrossFadeInFixedTime(currentAnimationState, 0.2f);
    }

    // ------------------- //
    // ATTACKING BEHAVIOUR //
    // ------------------- //

    [Header("Attacking")]
    public float attackDistance = 3f;
    public float attackDelay = 0.4f;
    public float attackSpeed = 1f;
    public int attackDamage = 1;

    public AudioClip swordSwing;
    public AudioClip defaultHitSound;
    public AudioClip playerHitSound;
    private bool attacking = false;
    private bool readyToAttack = true;
    private int attackCount;

    public void Attack(InputActionEventData inputData)
    {
        if (!readyToAttack || attacking)
            return;

        readyToAttack = false;
        attacking = true;

        Invoke(nameof(ResetAttack), attackSpeed);
        Invoke(nameof(Attack), attackDelay);

        audioSource.pitch = Random.Range(0.9f, 1.1f);
        audioSource.PlayOneShot(swordSwing);

        if (attackCount == 0)
        {
            ChangeAnimationState(ATTACK1);
            attackCount++;
        }
        else
        {
            ChangeAnimationState(ATTACK2);
            attackCount = 0;
        }
    }

    private void ResetAttack()
    {
        attacking = false;
        readyToAttack = true;
    }

    private void Attack() { }

    // only owner hears this effect, TODO port to rpc and spawnable audio source
    public void HitEffect(Vector3 pos, bool flesh)
    {
        audioSource.pitch = 1;
        audioSource.PlayOneShot(flesh ? playerHitSound : defaultHitSound);
    }
}
