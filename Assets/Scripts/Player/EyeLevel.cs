using Unity.Netcode;
using UnityEngine;

public class EyeLevel : NetworkBehaviour
{
    [SerializeField] private float defaultEyeLevel = 1.57f;
    [SerializeField] private float crouchedEyeLevel = 1f;

    [SerializeField] private float eyeLevelChangeSpeed = 1f;

    private Vector3 targetEyeLevel;
    private Transform _transform;

    private void Awake()
    {
        _transform = transform;
    }

    private void Start()
    {
        targetEyeLevel = Vector3.zero;
        SetIsCrouched(false);
    }

    public void SetIsCrouched(bool isCrouched)
    {
        targetEyeLevel.y = isCrouched ? crouchedEyeLevel : defaultEyeLevel;
    }

    [ServerRpc]
    public void SetIsCrouchedServerRPC(bool isActive)
    {
        SetIsCrouchedClientRpc(isActive);
    }

    [ClientRpc]
    private void SetIsCrouchedClientRpc(bool isActive)
    {
        SetIsCrouched(isActive);
    }

    private void Update()
    {
        _transform.localPosition = Vector3.MoveTowards(_transform.localPosition, targetEyeLevel, eyeLevelChangeSpeed * Time.deltaTime);
    }
}
