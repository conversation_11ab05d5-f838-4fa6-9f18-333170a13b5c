using MalteHusung.GlobalMessage;
using UnityEngine;

public class MainMenuPropsSpawner : MonoBehaviour
{
    [SerializeField]
    private GameObject propsPrefab;

    private GameObject _propsInstance;

    private void Awake()
    {
        MessageType.AddReceiver<ReturnToMainMenuMessage>(OnReturnToMainMenu);
        MessageType.AddReceiver<GameStartedMessage>(OnGameStarted);
    }

    private void Start()
    {
        OnReturnToMainMenu();
    }

    private void OnReturnToMainMenu()
    {
        if (_propsInstance == null)
            _propsInstance = Instantiate(propsPrefab, Vector3.zero, Quaternion.identity);
    }

    private void OnGameStarted()
    {
        if (_propsInstance != null)
        {
            Destroy(_propsInstance);
            _propsInstance = null;
        }
    }
}
