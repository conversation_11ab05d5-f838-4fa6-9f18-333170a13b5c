using System;
using Consts;
using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;
using Utils.PrefabManagement;

[HelpURL(StringConsts.DocumentationLink + "NT-A-90")]
public class DecalsCaster : SphereCaster, IDisposable
{
    [field: SerializeField] public DecalMaterialSettings materialSettings { get; set; }

    [Button]
    public void Explode(int randomSeed)
    {
        Explode(new System.Random(randomSeed));
    }

    public void Explode(System.Random random)
    {
        CastSphere(random);

        if (NetworkManager.Singleton.IsServer)
            foreach (var raycastHit in CastResults)
                DecalsManager.Instance.ServerSpawnDecal(new(raycastHit, transform.position), materialSettings);
    }

    public void Dispose()
    {
        PrefabSpawner.Instance.Despawn(gameObject);
    }
}
