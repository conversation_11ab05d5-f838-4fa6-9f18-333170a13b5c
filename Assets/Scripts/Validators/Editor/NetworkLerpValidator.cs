using Sirenix.OdinInspector.Editor.Validation;
using Unity.Netcode.Components;
using UnityEditor;
using UnityEngine;
using Validators;

[assembly: RegisterValidationRule(typeof(NetworkTransformLerpValidator))]

namespace Validators
{
    public class NetworkTransformLerpValidator : RootObjectValidator<GameObject>
    {
        protected override bool CanValidateObject(GameObject gameObject)
        {
            var assetPath = AssetDatabase.GetAssetPath(gameObject);
            var mainAsset = AssetDatabase.LoadMainAssetAtPath(assetPath);
            if (gameObject != mainAsset)
                return false;

            return gameObject != null && PrefabUtility.IsPartOfPrefabAsset(gameObject) && gameObject.GetComponent<NetworkTransform>() != null;
        }

        protected override void Validate(ValidationResult validationResult)
        {
            var networkTransform = Value.GetComponent<NetworkTransform>();

            var isValid = networkTransform.PositionInterpolationType == NetworkTransform.InterpolationTypes.Lerp &&
                          networkTransform.RotationInterpolationType == NetworkTransform.InterpolationTypes.Lerp;

            if (!isValid)
            {
                validationResult.AddError($"Prefab '{Value}' does not have the correct lerp type in NetworkTransform.").WithFix(
                    () =>
                    {
                        networkTransform.PositionInterpolationType = NetworkTransform.InterpolationTypes.Lerp;
                        networkTransform.RotationInterpolationType = NetworkTransform.InterpolationTypes.Lerp;
                        EditorUtility.SetDirty(networkTransform.gameObject);
                    });
            }
        }
    }
}