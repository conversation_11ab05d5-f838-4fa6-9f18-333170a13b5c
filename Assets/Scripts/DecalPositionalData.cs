using Unity.Netcode;
using UnityEngine;
using Utils.Extensions;

[System.Serializable]
public struct DecalPositionalData : INetworkSerializable
{
    public NetworkObjectReference ObjectToAttachTo;
    public Quaternion LocalRotation;
    public Vector3 LocalPosition;
    public Vector2 PositiveNegativeU;
    public Vector2 PositiveNegativeV;
    public float AxisDirectionSign;

    public DecalPositionalData(NetworkObjectReference objectToAttachTo, Vector3 localPosition, Quaternion localRotation, Vector2 positiveNegativeU, Vector2 positiveNegativeV, float axisDirectionSign)
    {
        ObjectToAttachTo = objectToAttachTo;
        LocalPosition = localPosition;
        LocalRotation = localRotation;
        PositiveNegativeU = positiveNegativeU;
        PositiveNegativeV = positiveNegativeV;
        AxisDirectionSign = axisDirectionSign;
    }

    public DecalPositionalData(RaycastHit raycastHit, Vector3 rayOrigin)
    {
        ProcessDecalCut(raycastHit, rayOrigin, out var decalUp, out var positiveNegativeU, out var positiveNegativeV, out var sideQuadrantSign);

        PositiveNegativeU = positiveNegativeU;
        PositiveNegativeV = positiveNegativeV;
        AxisDirectionSign = sideQuadrantSign;

        GetGlobalPositionRotation(raycastHit, decalUp, out var globalPosition, out var globalRotation);

        var networkObject = raycastHit.transform.gameObject.GetComponent<NetworkObject>();
        var networkReference = networkObject == null ? default : new NetworkObjectReference(networkObject);

        ObjectToAttachTo = networkReference;

        if (networkObject == null)
        {
            LocalPosition = globalPosition;
            LocalRotation = globalRotation;
        }
        else
        {
            var localPosition = raycastHit.transform.InverseTransformPoint(globalPosition);
            var localRotation = Quaternion.Inverse(raycastHit.transform.rotation) * globalRotation;

            LocalPosition = localPosition;
            LocalRotation = localRotation;
        }
    }

    public void NetworkSerialize<T>(BufferSerializer<T> serializer) where T : IReaderWriter
    {
        serializer.SerializeValue(ref ObjectToAttachTo);
        serializer.SerializeValue(ref LocalPosition);
        serializer.SerializeValue(ref LocalRotation);
        serializer.SerializeValue(ref PositiveNegativeU);
        serializer.SerializeValue(ref PositiveNegativeV);
        serializer.SerializeValue(ref AxisDirectionSign);
    }

    public static void GetGlobalPositionRotation(RaycastHit raycastHit, Vector3 up, out Vector3 globalPosition, out Quaternion globalRotation)
    {
        globalPosition = raycastHit.point;
        globalRotation = (-raycastHit.normal).ToRotation3D(up);
    }

    public static void ProcessDecalCut(RaycastHit raycastHit, Vector3 rayOrigin, out Vector3 decalUp, out Vector2 posNegUDimensions, out Vector2 posNegVDimensions, out float axisDirectionSign)
    {
        // get from decal, if necessary
        var decalSize = Vector2.one;

        decalUp = raycastHit.point - rayOrigin;

        // dimentions are half of the size
        var decalDimensions = decalSize / 2f;

        posNegUDimensions = decalDimensions;
        posNegVDimensions = decalDimensions;
        axisDirectionSign = 1f;

        // TODO think about placement on the side of a mesh collider
        if (raycastHit.collider is not BoxCollider boxCollider)
            return;

        // negate global position and scale of hit object, point is between 0 and collider.size
        var localSpaceHitPoint = raycastHit.collider.transform.InverseTransformPoint(raycastHit.point) - boxCollider.center;
        var targetWorldScale = raycastHit.collider.transform.lossyScale;

        var boxDimensions = boxCollider.size / 2f;

        // determine which side of the box the hit point is
        var axis = FloatExtensions.Approximately(Mathf.Abs(localSpaceHitPoint.x), boxDimensions.x, 0.0001f) ? Axis.X :
                   FloatExtensions.Approximately(Mathf.Abs(localSpaceHitPoint.y), boxDimensions.y, 0.0001f) ? Axis.Y :
                   FloatExtensions.Approximately(Mathf.Abs(localSpaceHitPoint.z), boxDimensions.z, 0.0001f) ? Axis.Z : default;

        axisDirectionSign = axis switch
        {
            Axis.X => Mathf.Sign(localSpaceHitPoint.x),
            Axis.Y => Mathf.Sign(localSpaceHitPoint.y),
            // why? who knows
            Axis.Z => -Mathf.Sign(localSpaceHitPoint.z),
            _ => default
        };

        GetAvailableSpace(boxDimensions,
                          localSpaceHitPoint,
                          targetWorldScale,
                          axis,
                          out var posNegUSpace,
                          out var posNegVSpace);

        // assumes decals are square
        var diagonalDimension = decalDimensions.x * FloatExtensions.Sqrt2;

        var freeRotation = posNegUSpace.x >= diagonalDimension &&
                           posNegUSpace.y >= diagonalDimension &&
                           posNegVSpace.x >= diagonalDimension &&
                           posNegVSpace.y >= diagonalDimension;

        // TODO manage rotation in between these states

        var clampToAxis = posNegUSpace.x < decalDimensions.x ||
                          posNegUSpace.y < decalDimensions.x ||
                          posNegVSpace.x < decalDimensions.y ||
                          posNegVSpace.y < decalDimensions.y;

        if (clampToAxis)
        {
            // cut the hanging sides of decal projector
            // cutting behaves differently based on quadrant
            //sideQuadrantSign = new Vector2(posNegUSpace.x >= posNegUSpace.y ? 1 : -1, posNegVSpace.x >= posNegVSpace.y ? 1 : -1);
            //sideQuadrantSign.x *= axisDirectionSign;

            // decal can not exceed its original positiveNegativeU
            posNegUDimensions.x = Mathf.Min(decalDimensions.x, posNegUSpace.x);
            posNegUDimensions.y = Mathf.Min(decalDimensions.x, posNegUSpace.y);
            posNegVDimensions.x = Mathf.Min(decalDimensions.y, posNegVSpace.x);
            posNegVDimensions.y = Mathf.Min(decalDimensions.y, posNegVSpace.y);
        }

        var axisAlignedUp = axis == Axis.Y ? raycastHit.collider.transform.forward : raycastHit.collider.transform.up;
        if (!freeRotation)
            decalUp = axisAlignedUp;
    }

    private static void GetAvailableSpace(Vector3 boxDimensions,
                                   Vector3 localSpaceHitPoint,
                                   Vector3 boxScale,
                                   Axis sideAxis,
                                   out Vector2 UPosNegSpace,
                                   out Vector2 VPosNegSpace)
    {
        switch (sideAxis)
        {
            // distance from point to positive and negative sides of the collider
            // negate the scale inversion
            case Axis.X:
                UPosNegSpace = new Vector2(boxDimensions.z - localSpaceHitPoint.z, boxDimensions.z + localSpaceHitPoint.z) * boxScale.z;
                VPosNegSpace = new Vector2(boxDimensions.y - localSpaceHitPoint.y, boxDimensions.y + localSpaceHitPoint.y) * boxScale.y;
                break;
            case Axis.Y:
                UPosNegSpace = new Vector2(boxDimensions.x - localSpaceHitPoint.x, boxDimensions.x + localSpaceHitPoint.x) * boxScale.x;
                VPosNegSpace = new Vector2(boxDimensions.z - localSpaceHitPoint.z, boxDimensions.z + localSpaceHitPoint.z) * boxScale.z;
                break;
            case Axis.Z:
                UPosNegSpace = new Vector2(boxDimensions.x - localSpaceHitPoint.x, boxDimensions.x + localSpaceHitPoint.x) * boxScale.x;
                VPosNegSpace = new Vector2(boxDimensions.y - localSpaceHitPoint.y, boxDimensions.y + localSpaceHitPoint.y) * boxScale.y;
                break;
            default:
                UPosNegSpace = Vector2.one / 2f;
                VPosNegSpace = Vector2.one / 2f;
                break;
        }
    }
}
#nullable disable