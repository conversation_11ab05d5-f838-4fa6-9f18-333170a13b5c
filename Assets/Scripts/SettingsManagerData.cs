using UnityEngine;

[CreateAssetMenu]
public class SettingsManagerData : ScriptableObject
{
    public AnimationCurve MouseSensitivityXConversionCurve = AnimationCurve.Linear(0, 0, 100, 1);
    public AnimationCurve MouseSensitivityYConversionCurve = AnimationCurve.Linear(0, 0, 100, 1);
    public AnimationCurve JoystickSensitivityXConversionCurve = AnimationCurve.Linear(0, 0, 100, 1);
    public AnimationCurve JoystickSensitivityYConversionCurve = AnimationCurve.Linear(0, 0, 100, 1);
}
