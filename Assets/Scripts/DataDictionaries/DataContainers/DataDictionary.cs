using System;
using System.Linq;
using System.Collections.Generic;
using System.Reflection;
using Consts;
using Utils.Debug;
using UnityEngine;
using Sirenix.OdinInspector;
using Sirenix.Serialization;
using Object = UnityEngine.Object;
using Random = UnityEngine.Random;
//using System.Reflection;

#if UNITY_EDITOR

using UnityEditor;

#endif

namespace DataDictionaries
{
    public abstract class DataDictionary<TData> : SerializedScriptableObject, IDataDictionary, IBindableScriptableObject where TData : UnityEngine.Object
    {
        #region Debug

        protected static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
        private static readonly Type type = typeof(DataDictionary<TData>);

        #endregion

        #region Dictionary ID

        public Type DataType => typeof(TData);

        public abstract byte DictionaryID { get; }

        /// Only used in inspector
        [ShowInInspector, ReadOnly, PropertyOrder(-1)]
        public string DictionaryIDDisplayed => DictionaryID.ToString("D3");
        
        #endregion

        #region Data & Binding

        [OdinSerialize, DictionaryDrawerSettings(KeyColumnWidth = 220)]
        protected Dictionary<DataAssetId, TData> _dictionary = new();

        private static Dictionary<DataAssetId, TData> Dictionary;

        public void Bind() => Dictionary = _dictionary;

        #endregion

#if UNITY_EDITOR
        
        [Button]
        public virtual void Update()
        {
            _dictionary ??= new Dictionary<DataAssetId, TData>();


            var keys = _dictionary.Keys.ToList();

            foreach (var key in keys)
            {
                if (_dictionary[key] == null)
                    _dictionary.Remove(key);
            }
            
            var assetGuids = AssetDatabase.FindAssets("t: " + typeof(TData).Name);

            foreach (var assetGuid in assetGuids)
            {
                var assets = AssetDatabase.LoadAllAssetsAtPath(AssetDatabase.GUIDToAssetPath(assetGuid));

                foreach (var asset in assets.OfType<ParentDataAsset>())
                {
                    if (asset is not TData)
                        continue;

                    if (asset.Id.IsEmpty)
                        SetId(asset, GetFreeAssetId());
                    
                    if (_dictionary.TryGetValue(asset.Id, out var existingAsset))
                    {
                        if (existingAsset == asset) continue;
                        
                        SetId(asset, GetFreeAssetId());
                    }
                    
                    EditorUtility.SetDirty(asset);
                    _dictionary.Add(asset.Id, asset as TData);
                }
            }

            CustomPostUpdate();

            EditorUtility.SetDirty(this);
        }

        public void Remove(ParentDataAsset item)
        {
            _dictionary.Remove(item.Id);
        }

        private void SetId(object asset, DataAssetId id)
        {
            var idProperty = typeof(ParentDataAsset).GetProperty("Id", BindingFlags.Instance | BindingFlags.Public);
            idProperty.SetValue(asset, id);
        }

        [Button]
        public void SaveAll()
        {
            foreach (var data in _dictionary.Values)
            {
                if (data == null)
                    continue;

                EditorUtility.SetDirty(data);
            }

            AssetDatabase.SaveAssets();
        }

        #region Debug

        [Button, FoldoutGroup(StringConsts.DebugFoldoutGroup, Order = 100)]
        public void ClearDictionary() => _dictionary.Clear();
        
        #endregion

#endif

        public virtual void CustomPostUpdate() { }

        #region Inspector Find Data

        private const string FindDataFoldoutGroup = "Find Data";

        public TData TryFindDataByAnything(string query)
        {
            var data = FindDataByGuid(query);
            if (data == default)
                data = FindGuidByName(query);
            return data;
        }

        [Button, FoldoutGroup(FindDataFoldoutGroup)]
        public TData FindDataByGuid(string guidPart)
        {
            var foundPairs = _dictionary.Where(pair => pair.Key.ToString().Contains(guidPart.ToLower()));
            return InspectResults(foundPairs);
        }

        [Button, FoldoutGroup(FindDataFoldoutGroup)]
        public TData FindGuidByName(string namePart)
        {
            var foundPairs = _dictionary.Where(pair => pair.Value.name.ToLower().Contains(namePart.ToLower()));
            return InspectResults(foundPairs);
        }

        private TData InspectResults(IEnumerable<KeyValuePair<DataAssetId, TData>> foundPairs)
        {
            TData data = default;
            var count = foundPairs.Count();
            switch (count)
            {
                case 0:
                    Debug.Log("Data not found");
                    break;
                case 1:
                    Debug.Log($"Found data - [{foundPairs.First().Key}] [{foundPairs.First().Value.name}]");
                    data = foundPairs.First().Value;
                    break;
                default:
                    Debug.Log($"Found [{count}] datas, please provide more exact entry");
                    break;
            }

            return data;
        }

        private DataAssetId GetFreeAssetId()
        {
            var maxId = _dictionary.Count == 0 ? (ushort)0 : _dictionary.Keys.Max(item => item.id);
            
            return new DataAssetId(DictionaryID, ++maxId);
        }

        #endregion

        //public abstract DataAssetId GeDataAssetIdFromAsset(TData data);

        #region Static Access Methods

        public static TData GetData(DataAssetId id)
        {
            var value = Dictionary.GetValueOrDefault(id);

            if (value == null)
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                    DebugMessenger.Message($"Value for key [{id}] was not found in [{type.Name}] data dictionary");

            return value;
        }
        
        public Object GetObject(DataAssetId id)
        {
            return Dictionary.GetValueOrDefault(id);
        }

        public static IEnumerable<TData> GetAllData() => Dictionary.Values;

        public static bool TryGetData(DataAssetId id, out TData data)
        {
            if (Dictionary.TryGetValue(id, out data))
            {
                return true;
            }

            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                DebugMessenger.Message($"Value for key [{id}] was not found in [{type.Name}] data dictionary");

            return false;
        }

        public static bool ContainsKey(DataAssetId key) => Dictionary?.ContainsKey(key) ?? false;
        public bool ContainsKeyEditor(DataAssetId key) => _dictionary.ContainsKey(key);

        #endregion
    }
}