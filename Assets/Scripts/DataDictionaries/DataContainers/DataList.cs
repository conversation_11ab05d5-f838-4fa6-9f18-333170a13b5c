using System.Collections.Generic;
using Sirenix.OdinInspector;

#if UNITY_EDITOR

using UnityEditor;

#endif

namespace DataDictionaries
{
    public class DataList<TData> : SerializedScriptableObject where TData : UnityEngine.Object
    {
        public List<TData> items = new();

#if UNITY_EDITOR

        [Button]
        public virtual void Update()
        {
            items.Clear();
            foreach (var element in AssetDatabase.FindAssets("t: " + typeof(TData).Name))
            {
                var asset = AssetDatabase.LoadAssetAtPath(AssetDatabase.GUIDToAssetPath(element), typeof(TData)) as TData;
                items.Add(asset);
            }

            EditorUtility.SetDirty(this);
        }

        [Button]
        public void SaveAll()
        {
            foreach (var item in items)
                EditorUtility.SetDirty(item);

            AssetDatabase.SaveAssets();
        }

#endif
    }
}