using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.Rendering;

[ExecuteInEditMode]
public class RenderMeshInScene : MonoBehaviour
{
    [SerializeField] private Material material;
    [SerializeField] private List<Mesh> meshes = new();

    [SerializeField] private GameObject meshesContainer;

    private Matrix4x4 transformMatrix;
    private RenderParams renderParams;

#if UNITY_EDITOR

    private void Awake()
    {
        RenderPipelineManager.beginCameraRendering += HandleRenderStart;

        if (Application.isPlaying == false)
            UnityEditor.SceneView.beforeSceneGui += OnSceneGUI;

        transformMatrix = transform.localToWorldMatrix;
        renderParams = new(material) { layer = 0, camera = Camera.main };
    }

    private void OnDestroy()
    {
        RenderPipelineManager.beginCameraRendering -= HandleRenderStart;

        if (Application.isPlaying == false)
            UnityEditor.SceneView.beforeSceneGui -= OnSceneGUI;
    }

    private void OnSceneGUI(UnityEditor.SceneView sceneView)
    {
        RenderMeshes(sceneView.camera);
    }

#endif

    [Button]
    private void ExtractMeshesFromContainer()
    {
        meshes.Clear();

        if (meshesContainer.TryGetComponent<GenericGrabbable>(out var genericGrabbable))
            meshes.AddRange(genericGrabbable.Meshes);

        // else
        // do manually if needed, - see GenericGrabbable.Meshes code
    }

    private void HandleRenderStart(ScriptableRenderContext context, Camera cam)
    {
        RenderMeshes(cam);
    }

    private void RenderMeshes(Camera cam)
    {
        renderParams.camera = cam;

        for (var i = 0; i < meshes.Count; i++)
            Graphics.RenderMesh(renderParams, meshes[i], 0, transformMatrix);
    }
}