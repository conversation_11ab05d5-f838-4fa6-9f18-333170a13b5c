using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using DataDictionaries;
using FMODUnity;
using MalteHusung.GlobalMessage;
using Newtonsoft.Json;
using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;
using UnityEngine.Localization;

public interface IShop
{
    void AddToCart(InventoryItem item);
    int InventoryPlusCartItemCount(InventoryItem item);
    int CartItemCount(InventoryItem item);
    int TotalCartItemCount { get; }
    bool MaxItemCountReached(InventoryItem item);
    IEnumerable<InventoryItem> CartItems { get; }
    int TotalPrice { get; }
    void RemoveFromCart(int itemIndex);
    void Deliver();
}

public interface IShopItem
{
    public DataAssetId Id { get; }
    public LocalizedString LocalizedName { get; }
    public Sprite Preview { get; }
    public Sprite MiniIcon { get; }
    public int Price { get; }
    public int MaxCount { get; }
    GameObject PreviewPrefab { get; }
}

public class Shop : NetworkBehaviorManager<IShop>, IShop
{
    public IEnumerable<InventoryItem> CartItems => _cartItems;

    public int TotalPrice => _cartItems.Sum(item => item.Price);
    public string SerializedCart => JsonConvert.SerializeObject(_cartItems);

    private readonly List<InventoryItem> _cartItems = new();

    protected override void SetInstance()
    {
        Instance = this;
    }

    [SerializeField] private EventReference leaveElevatorWarningSound;
    
    private void OnEnable()
    {
        MessageType.AddReceiver<ElevatorDoorsClosedMessage>(OnElevatorDoorsClosed);
        MessageType.AddReceiver<ElevatorDoorsOpenedMessage>(OnElevatorDoorsOpened);
    }

    private void OnDisable()
    {
        MessageType.RemoveReceiver<ElevatorDoorsClosedMessage>(OnElevatorDoorsClosed);
        MessageType.RemoveReceiver<ElevatorDoorsOpenedMessage>(OnElevatorDoorsOpened);
    }

    private void OnElevatorDoorsClosed()
    {
        if (!IsServer) return;

        ProcessDelivery().Forget();
    }

    private async UniTaskVoid ProcessDelivery()
    {
        if (GameStateTracker.Instance.CurrentState != GameStateTracker.GameState.ClosingForDelivery) return;

        GameStateTracker.Instance.SwitchCurrentGameState(GameStateTracker.GameState.DeliveryArriving);

        await UniTask.Yield();

        foreach (var cartItem in _cartItems)
        {
            Inventory.Instance.AddItem(cartItem);
        }

        ClearCart();

        await UniTask.Yield();

        GameStateTracker.Instance.SwitchCurrentGameState(GameStateTracker.GameState.OpeningAfterDelivery);
    }

    private void OnElevatorDoorsOpened()
    {
        if (GameStateTracker.Instance.CurrentState != GameStateTracker.GameState.OpeningAfterDelivery) return;

        GameStateTracker.Instance.SwitchCurrentGameState(GameStateTracker.GameState.StartRoom);
    }

    #region AddToCart

    public void AddToCart(InventoryItem item)
    {
        AddToCartInternal(item);

        if (IsServer)
        {
            FullCartSync();
        }
        else
        {
            AddToCartServerRpc(item.Id);
        }
    }

    private void AddToCartInternal(InventoryItem item)
    {
        if (MaxItemCountReached(item))
            return;

        if (TotalPrice + item.Price > Inventory.Instance.GetCurrencyAmount(Currency.Cocos))
        {
            NotEnoughCurrency.Send<NotEnoughCurrency>(Currency.Cocos, this);
            return;
        }

        _cartItems.Add(item);
        MessageType.Send<ShopCartChangedMessage>(this);
    }

    [ServerRpc(RequireOwnership = false)]
    private void AddToCartServerRpc(DataAssetId dataAssetId)
    {
        var item = InventoryItemDataDictionary.GetData(dataAssetId);

        Debug.Log($"Adding {item} via server rpc");

        AddToCartInternal(item);
        FullCartSync();
    }

    #endregion


    #region RemoveFromCart

    public void RemoveFromCart(int itemIndex)
    {
        RemoveFromCartInternal(itemIndex);

        if (IsServer)
        {
            FullCartSync();
        }
        else
        {
            RemoveFromCartServerRpc(itemIndex);
        }
    }

    private void RemoveFromCartInternal(int itemIndex)
    {
        _cartItems.RemoveAt(itemIndex);
        MessageType.Send<ShopCartChangedMessage>(this);
    }

    [ServerRpc(RequireOwnership = false)]
    private void RemoveFromCartServerRpc(int itemIndex)
    {
        RemoveFromCartInternal(itemIndex);
        FullCartSync();
    }

    #endregion

    #region ClearCart

    public void ClearCart()
    {
        ClearCartInternal();

        if (IsServer)
        {
            FullCartSync();
        }
        else
        {
            ClearCartServerRpc();
        }
    }

    private void ClearCartInternal()
    {
        _cartItems.Clear();
        MessageType.Send<ShopCartChangedMessage>(this);
    }

    [ServerRpc(RequireOwnership = false)]
    private void ClearCartServerRpc()
    {
        ClearCartInternal();
        FullCartSync();
    }

    #endregion

    private void FullCartSync()
    {
        FullCartSyncClientRpc(JsonConvert.SerializeObject(_cartItems));
    }

    [ClientRpc]
    private void FullCartSyncClientRpc(string serializedCart)
    {
        _cartItems.Clear();
        JsonConvert.PopulateObject(serializedCart, _cartItems);
        MessageType.Send<ShopCartChangedMessage>(this);
    }

    public int CartItemCount(InventoryItem item)
    {
        var count = 0;
        foreach (var shopItem in _cartItems)
        {
            if (shopItem == item)
                count++;
        }

        return count;
    }

    public int TotalCartItemCount => _cartItems.Count; 

    [Button]
    public void DumpSerializedCart()
    {
        Debug.Log(JsonConvert.SerializeObject(_cartItems));
    }

    public int InventoryPlusCartItemCount(InventoryItem item) => Inventory.Instance.ItemCount(item) + CartItemCount(item);

    public bool MaxItemCountReached(InventoryItem item) => InventoryPlusCartItemCount(item) >= item.MaxCount;

    public void Deliver()
    {
        DeliverServerRpc();
    }

    [ClientRpc]
    private void PlayLeaveElevatorWarningClientRpc()
    {
        Elevator.Instance.PlayRadioMessage(leaveElevatorWarningSound.Guid);
    }

    [ServerRpc(RequireOwnership = false)]
    private void DeliverServerRpc()
    {
        if (_cartItems.Count == 0) return;

        if (Inventory.Instance.GetCurrencyAmount(Currency.Cocos) < TotalPrice)
        {
            return;
        }

        if (Elevator.HasPlayers)
        {
            PlayLeaveElevatorWarningClientRpc();
            return;
        }

        Debug.Log("Deliver");

        Inventory.Instance.SpendCurrency(Currency.Cocos, TotalPrice);

        GameStateTracker.Instance.SwitchCurrentGameState(GameStateTracker.GameState.ClosingForDelivery);
    }
}