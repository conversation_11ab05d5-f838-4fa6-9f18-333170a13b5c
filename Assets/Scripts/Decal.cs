using System.Collections;
using System.Collections.Generic;
using Consts;
using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEngine.UIElements;
using Utils.Debug;
using Utils.Extensions;

[HelpURL(StringConsts.DocumentationLink + "NT-A-161")]
public class Decal : MonoBehaviour
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(Decal);

    #endregion

    public Material BoundMaterial { get; private set; }
    public DecalProjector DecalProjector => projector;
    public ParticleSystem ParticleSystem => particleSystem;
    public int HitsToClean { get; set; }
    public bool IsSoftened { get; private set; }

    // do not use it internally, only for server side DecalsManager use
    public DecalMaterialSettings ServerDecalEventSettingsLookup { get; set; }

    [SerializeField] private DecalProjector projector;
    [SerializeField] private new ParticleSystem particleSystem;

    [Serial<PERSON>Field, ReadOnly] private Vector2 positiveNegativeU;
    [SerializeField, ReadOnly] private Vector2 positiveNegativeV;

    private List<ParticleCollisionEvent> _collisionEvents = new();

    public static readonly int DecalIndexID = Shader.PropertyToID("_DecalIndex");
    public static readonly int PaletteIndexID = Shader.PropertyToID("_PaletteIndex");
    public static readonly int NoiseSeedsID = Shader.PropertyToID("_NoiseSeeds");
    public static readonly int CleanProgressID = Shader.PropertyToID("_CleanProgress");
    public static readonly int SoftenProgressID = Shader.PropertyToID("_SoftenProgress");

    public static readonly int BlinkProgressID = Shader.PropertyToID("_BlinkProgress");

    private bool _materialInitialized = false;
    private int currentIndex;
    private DecalSettings _gameConfigDecalSettings;
    private readonly Collider[] _overlapResults = new Collider[2];

    public static readonly Vector3 ProjectorSize = new(1f, 1f, 0.1f);
    public static readonly Vector3 ProjectorPivot = new(0f, 0f, 0.025f);
    public static readonly Vector3 ColliderSize = new(0.75f, 0.75f, 0.25f);

    private void Awake()
    {
        if (!projector)
            projector = GetComponent<DecalProjector>();
        if (!particleSystem)
            particleSystem = GetComponent<ParticleSystem>();
    }

    #region Init

    /// <summary>
    /// First time initialization and values reset
    /// </summary>
    public void Initialize(DecalSettings decalSettings)
    {
        _gameConfigDecalSettings = decalSettings;
        Initialize(_gameConfigDecalSettings.DecalUberMaterial, _gameConfigDecalSettings.HitsToClean);
    }

    private void Initialize(Material originalMaterial, int hitsToClean)
    {
        if (!_materialInitialized)
        {
            // destroys instancing
            BoundMaterial = new Material(originalMaterial);
            projector.material = BoundMaterial;

            BoundMaterial.name = $"{BoundMaterial.name}_Instance[{BoundMaterial.GetInstanceID()}]";
            _materialInitialized = true;
        }

        ResetValues(hitsToClean);
    }

    public void ResetValues(int hitsToClean)
    {
        HitsToClean = hitsToClean;

        BoundMaterial.SetFloat(CleanProgressID, 0f);
        BoundMaterial.SetFloat(SoftenProgressID, 0f);

        IsSoftened = false;
    }

    #endregion

    #region Setup

    public void Setup(int decalIndex, ref DecalPositionalData decalPositioning, DecalVisualData decalVisuals)
    {
        currentIndex = decalIndex;

        SetupMaterial(decalVisuals);
        SetupParticleSystem(decalVisuals.PaletteIndex, decalVisuals.ParticlesStartDelay);
        SetupPositionRotation(decalPositioning);

        // noone is disabling them, yet sometimes they appear disabled
        this.enabled = true;
        DecalProjector.enabled = true;
    }


    private void SetupMaterial(DecalVisualData decalVisuals)
    {
        BoundMaterial.SetVector(DecalIndexID, decalVisuals.ShapeIndex);
        BoundMaterial.SetFloat(PaletteIndexID, decalVisuals.PaletteIndex);
        BoundMaterial.SetVector(NoiseSeedsID, decalVisuals.NoiseSeeds);
    }

    private void SetupParticleSystem(int materialIndex, float delay)
    {
        var sourceTexture = _gameConfigDecalSettings.DecalPalette;
        var mainModule = ParticleSystem.main;
        mainModule.startColor = new ParticleSystem.MinMaxGradient(sourceTexture.GetPixel(materialIndex, 0),
                                                                  sourceTexture.GetPixel(materialIndex, 1));

        particleSystem.useAutoRandomSeed = false;
        particleSystem.randomSeed = (uint)(delay * 1000000);

        mainModule.startDelay = delay;
    }

    private void SetupPositionRotation(DecalPositionalData decalPositioning)
    {
        PositionRotationFromData(transform, decalPositioning);

        if (transform.lossyScale != Vector3.one)
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Warning))
                DebugMessenger.Message($"Decal scale may be incorrect");

        SetProjectorSize(decalPositioning.PositiveNegativeU, decalPositioning.PositiveNegativeV, decalPositioning.AxisDirectionSign);
    }
    
    public static void PositionRotationFromData(Transform ownTransform, DecalPositionalData decalPositionalData)
    {
        // if networkObject is empty, positions are actually global

        ownTransform.localScale = Vector3.one;
        decalPositionalData.ObjectToAttachTo.TryGet(out var networkObject);
        if (networkObject != null)
            ownTransform.SetParent(networkObject.transform);

        ownTransform.SetLocalPositionAndRotation(decalPositionalData.LocalPosition, decalPositionalData.LocalRotation);
    }

    [Button]
    public void SetProjectorSize(Vector2 positiveNegativeU, Vector2 positiveNegativeV, float axisDirectionSign)
    {
        this.positiveNegativeU = positiveNegativeU;
        this.positiveNegativeV = positiveNegativeV;

        var size = new Vector3(positiveNegativeU.x + positiveNegativeU.y, positiveNegativeV.x + positiveNegativeV.y, projector.size.z);
        var uvScale = (Vector2)size;

        var offset = new Vector3(positiveNegativeU.x - positiveNegativeU.y, positiveNegativeV.x - positiveNegativeV.y, 2 * ProjectorPivot.z);
        var pivot = offset.WithX(offset.x * axisDirectionSign) / 2f;

        var uvBias = Vector2.zero;

        if (axisDirectionSign < 0)
            (positiveNegativeU.x, positiveNegativeU.y) = (positiveNegativeU.y, positiveNegativeU.x);

        // past this point I have no idea why it is the way it is

        if (positiveNegativeU.y >= positiveNegativeU.x)
        {
            if (positiveNegativeU.y < 0.5f)
                uvBias.x = 0.5f - positiveNegativeU.y;
            else
                uvBias.x = 0;
        }
        else
        {
            if (positiveNegativeU.x < 0.5f)
                uvBias.x = 0.5f - positiveNegativeU.y;
            else
                uvBias.x = Mathf.Clamp01(1 - size.x);
        }

        if (positiveNegativeV.y >= positiveNegativeV.x)
        {
            if (positiveNegativeV.y < 0.5f)
                uvBias.y = 0.5f - positiveNegativeV.y;
            else
                uvBias.y = 0;
        }
        else
        {
            if (positiveNegativeV.x < 0.5f)
                uvBias.y = 0.5f - positiveNegativeV.y;
            else
                uvBias.y = Mathf.Clamp01(1 - size.y);
        }

        // width / height
        projector.size = size;

        // pivot
        projector.pivot = pivot;

        // tiling
        projector.uvScale = uvScale;

        // offset
        projector.uvBias = uvBias;
    }

    #endregion

    #region Late Setup

    /// <summary>
    /// Final decisions on positionalDecal systems based on setup values
    /// </summary>
    public void LateSetup()
    {
        // Do not play particles if positionalDecal's placement does not make sense for it
        if (transform.forward.y > 0)
        {
            var shapeModule = ParticleSystem.shape;
            shapeModule.scale = transform.localScale.Inverse();
            ParticleSystem.Play();
        }
        else
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                DebugMessenger.Message($"Decal is facing upwards");
        }
    }

    #endregion

    #region Softening

    public bool TrySoften(int hitsToReduce, float duration)
    {
        if (IsSoftened)
            return false;

        HitsToClean = Mathf.Max(1, HitsToClean - hitsToReduce);
        IsSoftened = true;

        StartCoroutine(SofteningCoroutine(duration));

        return true;
    }

    private IEnumerator SofteningCoroutine(float duration)
    {
        var currentTime = 0f;

        while (currentTime < duration)
        {
            currentTime += Time.deltaTime;
            BoundMaterial.SetFloat(SoftenProgressID, currentTime / duration);

            yield return null;
        }
    }

    #endregion

    private void OnParticleCollision(GameObject objectParticleCollidedWith)
    {
        if (!NetworkManager.Singleton.IsServer)
            return;

        var numCollisionEvents = particleSystem.GetCollisionEvents(objectParticleCollidedWith, _collisionEvents);
        for (var i = 0; i < numCollisionEvents; i++)
        {
            // _overlapResults are not not cleared on use because are not used
            // decals will not spawn infinitely on one place (but may intersect by <0.5 of their positiveNegativeU)
            if (Physics.OverlapSphereNonAlloc(_collisionEvents[i].intersection, 0.01f, _overlapResults, LayerConsts.Decals.MaskFromLayer()) > 0)
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Drop landed within existing decal");

                continue;
            }

            var collider = objectParticleCollidedWith.GetComponent<Collider>();
            var emissionStartPosition = transform.position + particleSystem.shape.position;
            var collisionDirection = _collisionEvents[i].intersection - emissionStartPosition;

            _castRay = new(emissionStartPosition, collisionDirection);
            _collisionDirection = collisionDirection;

            collider.Raycast(new(emissionStartPosition, collisionDirection), out var raycastHit, collisionDirection.magnitude * 1.1f);
            if (!RaycastDecalFilter.IsEligibleForDecal(raycastHit)) continue;
            var materialIndex = (int)BoundMaterial.GetFloat(PaletteIndexID);
            // technically we already are on server, but this will keep things cleaner
            DecalsManager.Instance.ServerSpawnDecal(new(raycastHit, emissionStartPosition), currentIndex, materialIndex);
        }
    }

    private Ray _castRay;
    private Vector3 _collisionDirection;
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        var point1 = _castRay.origin;
        var point2 = _castRay.GetPoint(_collisionDirection.magnitude * 1.1f);
        Gizmos.DrawSphere(point1, 0.01f);
        Gizmos.DrawLine(point1, point2);
        Gizmos.DrawSphere(point2, 0.01f);
    }
}

#nullable disable