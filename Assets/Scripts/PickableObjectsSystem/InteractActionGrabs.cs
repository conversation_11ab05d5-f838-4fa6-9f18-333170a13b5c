using FMODUnity;
using Interactables;
using Rewired;
using Sirenix.OdinInspector;
using Unity.Netcode;
using Unity.Netcode.Components;
using UnityEngine;
using Utils.Debug;

/// <summary>
/// Grab / Drop Logic
/// </summary>
public partial class InteractAction
{
    [SerializeField] private Transform grabPoint;
    [Toolt<PERSON>("Portion of Sensor ItemGrabDistance after which the joint breaks")]
    [SerializeField, Range(1f, 2f)] private float autoDropDistanceThreshold = 1.1f;

    public float MaxGrabbableItemMass => maxItemMass.Value;
    [SerializeField] private OverridableFloatValue maxItemMass;
    [SerializeField] private OverridableFloatValue throwForce;

    [SerializeField] private float jointSpringLimitSpring;
    [SerializeField] private float jointSpringLimitDamp;

    [SerializeField] private float jointSlerpDriveSpring = 1000f;
    [SerializeField] private float jointSlerpDriveDamp = 100f;

    [SerializeField] private float jointMassScale = 10f;

    // successfull slot throw sound
    [SerializeField] private EventReference throwIntoSlotSound;
    // simple throw sound
    [SerializeField] private EventReference noSlotThrowSound;

    /// <summary>
    /// Item currently in hands, not a tool
    /// </summary>
    public IGrabbable ItemInHands => _itemInHands;
    [ShowInInspector] private IGrabbable _itemInHands;

    private ConfigurableJoint _itemInHandsJoint;
    private ConfigurableJointMotion _itemInHandsMotion;

    private bool _skipSameFrameThrow;

    #region Unity Callbacks

    private void FixedUpdate()
    {
        if (!IsOwner)
            return;

        if (_itemInHands as Object == null)
            _itemInHands = null;

        if (_itemInHands == null)
            return;

        CheckGrabbedItemDistance();
    }

    private void CheckGrabbedItemDistance()
    {
        if (_itemInHandsJoint == null)
            return;

        var grabbedTransform = _itemInHands.JointLocation == null ? _itemInHands.Transform : _itemInHands.JointLocation;
        var jointTargetPosition = grabbedTransform.TransformPoint(_itemInHandsJoint.anchor);
        var distance = Vector3.Distance(jointTargetPosition, grabPoint.position);
        if (distance > sensor.GetItemGrabDistance * autoDropDistanceThreshold)
            DropItem();
    }

    private void LateUpdate()
    {
        UpdateItemInHandsPositionGrabs();
        UpdateItemInHandsPositionTools();

        _skipSameFrameThrow = false;

        if (_state == State.PostIdle)
            _state = State.Idle;
    }

    private void UpdateItemInHandsPositionGrabs()
    {
        if (_itemInHandsJoint != null)
        {
            _itemInHandsJoint.connectedAnchor = transform.InverseTransformPoint(grabPoint.position);

            // stop item from following you on screen while waiting for RPCs to actually throw it
            if (_state == State.DropRequest && _itemInHandsMotion != ConfigurableJointMotion.Free)
                SetJointMotion(ConfigurableJointMotion.Free);
        }
    }

    private partial void UpdateItemInHandsPositionTools();

    protected partial void OnNetworkSpawnGrabs()
    {
        ResetGrabParameters();
    }

    protected partial void OnNetworkDespawnGrabs()
    {
        DropItem();
    }

    #endregion

    #region Input Event Delegates

    private partial void EnableInputGrabs()
    {
        _rewiredPlayer.AddInputEventDelegate(OnDrop, UpdateLoopType.Update, InputActionEventType.ButtonJustPressed, RewiredConsts.Action.Drop);
        _rewiredPlayer.AddInputEventDelegate(OnThrow, UpdateLoopType.Update, InputActionEventType.ButtonJustPressed, RewiredConsts.Action.Throw);
    }

    private partial void DisableInputGrabs()
    {
        _rewiredPlayer.RemoveInputEventDelegate(OnDrop);
        _rewiredPlayer.RemoveInputEventDelegate(OnThrow);
    }

    #endregion

    public void RemoteInteract()
    {
        OnInteract(default);
        _skipSameFrameThrow = true;
    }

    private partial void InteractWithObjectAllClientsGrabs(IGrabbable grabbable, Vector3 raycastHitPoint)
    {
        if (grabbable is ITool tool)
            InteractWithObjectAllClientsTools(tool);
        else
        {
            _itemInHands = grabbable;
            GrabWithJoint(grabbable, raycastHitPoint);
        }

        rightHandTransformMirror.Source = grabbable.RightHandIKPoint;
        leftHandTransformMirror.Source = grabbable.LeftHandIKPoint;
    }

    private partial void InteractWithObjectAllClientsTools(ITool tool);

    private void GrabWithJoint(IGrabbable grabbable, Vector3 raycastHitPoint)
    {
        var grabAtLocation = grabbable.JointLocation == null ? grabbable.GameObject : grabbable.JointLocation.gameObject;
        _itemInHandsJoint = grabAtLocation.AddComponent<ConfigurableJoint>();
        _itemInHandsJoint.connectedBody = GetComponent<Rigidbody>();

        _itemInHandsJoint.configuredInWorldSpace = true;
        // some items are wrong way around and have to be fixed or multiplied by Quaternion.Euler(0, 180, 0)
        //_itemInHandsJoint.SetTargetRotation(transform.rotation, grabAtLocation.transform.rotation);

        _itemInHandsJoint.anchor = grabAtLocation.transform.InverseTransformPoint(raycastHitPoint);

        SetJointMotion(ConfigurableJointMotion.Limited);

        _itemInHandsJoint.autoConfigureConnectedAnchor = false;
        _itemInHandsJoint.connectedAnchor = transform.InverseTransformPoint(grabPoint.position);

        _characterController.ItemMass = grabbable.Rigidbody.mass;
    }

    private void SetJointMotion(ConfigurableJointMotion motion)
    {
        _itemInHandsMotion = motion;

        _itemInHandsJoint.xMotion = motion;
        _itemInHandsJoint.yMotion = motion;
        _itemInHandsJoint.zMotion = motion;

        if (motion == ConfigurableJointMotion.Limited)
        {
            _itemInHandsJoint.rotationDriveMode = RotationDriveMode.Slerp;
            var slerpDrive = new JointDrive()
            {
                positionSpring = jointSlerpDriveSpring,
                positionDamper = jointSlerpDriveDamp,
                maximumForce = float.MaxValue
            };
            _itemInHandsJoint.slerpDrive = slerpDrive;

            // is it useful?
            var softJointSpring = new SoftJointLimitSpring
            {
                spring = jointSpringLimitSpring,
                damper = jointSpringLimitDamp
            };
            _itemInHandsJoint.linearLimitSpring = softJointSpring;

            _itemInHandsJoint.massScale = jointMassScale;
        }
        else
        {
            // do not disable slerp drive, item will start spinning uncontrollably
            // before server throw will catch up
            _itemInHandsJoint.massScale = 1f;
        }
    }

    #region Drop

    public void ForceDropItem()
    {
        DropItem();
    }

    public void ForceDropEverything()
    {
        DropItem(dropAllItems: true);
    }

    private void OnDrop(InputActionEventData inputData)
    {
        var itemToDrop = _itemInHands == null ? _equippedTool : _itemInHands;

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message(
                $"[Client {NetworkManager.LocalClientId}] pressed OnDrop() locally on [{this}, ID: {NetworkObjectId}] and wants to notify others about his [{itemToDrop}] that he wants to drop.");

        DropItem();
    }

    // single entry point for Force and Input sources of drop
    private void DropItem(Vector3 withForce = default, bool dropAllItems = false)
    {
        var itemToDrop = _itemInHands ?? _equippedTool;

        if (itemToDrop == null)
            return;

        if (_state == State.DropRequest && !dropAllItems)
            return;

        DropGrabbable(itemToDrop, withForce);

        if (dropAllItems)
        {
            var otherItem = _equippedTool ?? _itemInHands;
            if (otherItem != itemToDrop && otherItem != null)
                DropGrabbable(otherItem, withForce);
        }
    }

    private void DropGrabbable(IGrabbable itemToDrop, Vector3 withForce)
    {
        if (!itemToDrop.RequestDrop())
            return;

        _state = State.DropRequest;

        try
        {
            itemToDrop.DropOriginalClientSide(OwnerClientId, this);
        }
        catch (System.Exception e)
        {
            Debug.LogException(e);
        }

        AnimatorsSetBool(AnimatorConsts.ActionBool, false);

        // send actual local position to server transform (might become obsolete after Y-axis camera synchronization)
        var itemInHandsAsMono = itemToDrop as MonoBehaviour;
        if (itemInHandsAsMono == null)
            return;

        var itemTransform = itemInHandsAsMono.transform;
        var reference = new NetworkObjectReference(itemToDrop.NetworkObject);

        // TODO; noticeable delay before input and whole ordeal of "input -> serverRPC -> clientRPC -> item dropped on screen" on client
        PlayerDropObjectServerRPC(reference, itemTransform.position, itemTransform.rotation, OwnerClientId, withForce);

        if (EquippedTool != null && itemToDrop != EquippedTool)
            EquippedTool.OnDropUsingThisTool(this, withForce != default);
    }

    [ServerRpc(RequireOwnership = false)]
    private void PlayerDropObjectServerRPC(
        NetworkObjectReference objectReference,
        Vector3 clientAuthPosition,
        Quaternion clientAuthRotation,
        ulong clientID,
        Vector3 withForce = default,
        ServerRpcParams serverRpcParams = default)
    {
        if (!objectReference.TryGet(out var objectToDrop))
        {
            if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                DebugMessenger.Message($"Could not get NetworkObject from provided reference");

            return;
        }

        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message(
                $"Server (aka [Client {NetworkManager.LocalClientId}]) got message from [Client {serverRpcParams.Receive.SenderClientId}] about him dropping an item in hands. There should be a force of [{withForce}] applied. He is now transmitting it to other clients.");

        if (!objectToDrop.TryGetComponent<IGrabbable>(out var serverSideDroppable))
        {
            if ((serverSideDroppable = objectToDrop.GetComponentInChildren<IGrabbable>()) == null)
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                    DebugMessenger.Message($"Could not get IGrabbable on NetworkObject on server side");

                return;
            }
        }

        serverSideDroppable.DropServerSide(clientID);

        if ((serverSideDroppable as MonoBehaviour).TryGetComponent<NetworkTransform>(out var netTransform))
            netTransform.SetState(clientAuthPosition, clientAuthRotation, teleportDisabled: false);

        PlayerDropObjectClientRPC(withForce, clientID);
    }

    [ClientRpc]
    private void PlayerDropObjectClientRPC(Vector3 withForce, ulong clientID)
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message(
                $"[Client {NetworkManager.LocalClientId}] received a message exactly at [{this}, ID: {NetworkObjectId}] from Server (no info who) about with the fact that he should drop grabbable object at this instance.");

        var isTool = _itemInHands == null;
        var itemToDrop = _itemInHands == null ? _equippedTool : _itemInHands;

        if (itemToDrop != null)
        {
            _state = State.Dropping;

            var capturedItemInHandsReference = itemToDrop;

            var dropAtLocation = itemToDrop.JointLocation == null ? itemToDrop.GameObject : itemToDrop.JointLocation.gameObject;
            var joint = dropAtLocation.GetComponent<ConfigurableJoint>();

            try
            {
                // can not wait for safe destruction, because AddForce(withForce) won't work
                DestroyImmediate(joint);
            }
            finally
            {
                //Destroying components immediately is not permitted during physics trigger/contact,
                //animation event callbacks, rendering callbacks or OnValidate.
                //You must use DestroyOrDespawn instead.

                // if this happens, there is no force involved, just item release
                Destroy(joint);
            }

            rightHandTransformMirror.Source = null;
            leftHandTransformMirror.Source = null;

            itemToDrop.DropClientSide(IsOwner, clientID);
            _itemInHandsJoint = null;

            if (isTool)
                EquippedTool = null;
            else
                _itemInHands = null;

            _characterController.ItemMass = 0f;

            // can not be cleanly done in ServerRPC, because the joint is not destroyed yet
            if (IsServer)
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
                    DebugMessenger.Message($"Frame [{Time.frameCount}] : [{capturedItemInHandsReference.Rigidbody}] apply throwing force [{withForce}]");

                if (withForce != Vector3.back)
                    capturedItemInHandsReference.Rigidbody.AddForce(withForce);
            }

            if (EquippedTool == null)
                AnimatorsSetTrigger(AnimatorConsts.Idle);

            _state = State.PostIdle;
        }
    }

    #endregion

    #region Throw

    // OC context
    private void OnThrow(InputActionEventData inputData)
    {
        if (_itemInHands == null)
            return;

        if (EquippedTool != null && !EquippedTool.AllowGrabbingItems)
            return;

        if (_skipSameFrameThrow)
            return;

        ThrowItemInHands();
    }

    // OC context
    public void ThrowItemInHands()
    {
        if (sensor.ItemSlotInFocus != null)
        {
            if (_itemInHands is not ISnappable { CanBeSnapped: true })
                return;

            var capturedItemInHandsReference = _itemInHands;

            DropItem(Vector3.back);

            if (!throwIntoSlotSound.IsNull)
                SoundFXManager.Instance.PlayOneShot(MultiplayerContext.OriginalClient, throwIntoSlotSound.Guid, transform.position, SoundFXTarget.Local);

            if (capturedItemInHandsReference is HumanSleeper humanInHands)
                humanInHands.PurgeHipsOffset();

            sensor.ItemSlotInFocus.ReceiveItem(capturedItemInHandsReference.GameObject, OwnerClientId);
        }
        else
        {
            if (!noSlotThrowSound.IsNull)
                SoundFXManager.Instance.PlayOneShot(MultiplayerContext.OriginalClient, noSlotThrowSound.Guid, transform.position, SoundFXTarget.Local);

            DropItem(headCameraTransform.forward * throwForce.Value);
        }
    }

    #endregion

    public void OverrideGrabParameters(float newMaxItemMass, float newThrowForce, float itemSearchDistance, float slotSearchDistance)
    {
        maxItemMass.OverrideValue(newMaxItemMass);
        throwForce.OverrideValue(newThrowForce);

        sensor.OverrideCastDistances(itemSearchDistance, slotSearchDistance);
    }

    public void ResetGrabParameters()
    {
        maxItemMass.ResetValue();
        throwForce.ResetValue();

        sensor.ResetCastDistances();
    }
}