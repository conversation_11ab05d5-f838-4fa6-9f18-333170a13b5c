using System.Collections.Generic;
using Cinemachine;
using InputHandling;
using Rewired;
using Unity.Netcode;
using UnityEngine;

public class Terminal : GenericInteractable
{
    [SerializeField] private CinemachineVirtualCamera virtualCamera;
    [SerializeField] private Canvas canvas;
    [SerializeField] private Camera canvasCamera;

    public override bool RemoteInteractionAllowed => false;

    private readonly List<IInputListener> _inputListeners = new(32);
    private readonly NetworkVariable<ulong> _currentInteractorId = new();
    private bool _isInteracting;
    private const ulong NoInteractor = ulong.MaxValue;

    private void Start()
    {
        canvas.transform.position += Vector3.up * 1000f;
        canvas.worldCamera = canvasCamera;
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        GetComponentsInChildren(_inputListeners);
        canvas.worldCamera = Camera.main;
        
        _currentInteractorId.OnValueChanged = OnInteractorChanged;

        if (NetworkManager.IsServer)
            _currentInteractorId.Value = NoInteractor;
        
        Debug.Log(NetworkManager.LocalClientId);
        
    }

    private void OnInteractorChanged(ulong previousValue, ulong newValue)
    {
        if (_isInteracting && newValue != NetworkManager.LocalClientId)
            LeaveTerminal();
    }

    public override void InteractClientSide(IGrabbable itemInHands, bool isOwner, Vector3 raycastPoint, Vector3 headDirection, ulong clientId, InteractAction interactor)
    {
        base.InteractClientSide(itemInHands, isOwner, raycastPoint, headDirection, clientId, interactor);

        if (_isInteracting) return;
        if (clientId != NetworkManager.LocalClientId) return;
        if (_currentInteractorId.Value != NoInteractor) return;
        
        EnterTerminal();
    }

    private void OnUIBack(InputActionEventData obj)
    {
        if (!_isInteracting) return;
        
        LeaveTerminal();
    }

    [ServerRpc(RequireOwnership = false)]
    private void SetCurrentInteractorIdServerRPC(ulong interactorId)
    {
        if (_currentInteractorId.Value != NoInteractor)
            return;

        _currentInteractorId.Value = interactorId;
        NetworkObject.ChangeOwnership(interactorId);
    }
    
    [ServerRpc(RequireOwnership = false)]
    private void ResetCurrentInteractorIdServerRPC(ulong interactorId)
    {
        if (_currentInteractorId.Value != interactorId)
            return;

        _currentInteractorId.Value = NoInteractor;
        NetworkObject.ChangeOwnership(NetworkManager.ServerClientId);
    }

    private void EnterTerminal()
    {        
        SetMapEnabled(RewiredConsts.Category.Default, false);
        SetMapEnabled(RewiredConsts.Category.Terminal, true);
        virtualCamera.enabled = true;
        InGameMenuOpener.Instance.RegisterLocker(this);

        SetInputListenersEnabled(true);
        ReInput.players.GetPlayer(0).AddInputEventDelegate(OnUIBack, UpdateLoopType.Update, InputActionEventType.ButtonJustPressed, RewiredConsts.Action.UIBack);
        RequestHideHUDMessage.Send<RequestHideHUDMessage>(new RequestHideHUDMessageData { requestStatus = true, sender = this }, this);
        _isInteracting = true;
        SetCurrentInteractorIdServerRPC(NetworkManager.LocalClientId);
    }
    
    private void LeaveTerminal()
    {
        SetMapEnabled(RewiredConsts.Category.Default, true);
        SetMapEnabled(RewiredConsts.Category.Terminal, false);
        virtualCamera.enabled = false;
        InGameMenuOpener.Instance.UnregisterLocker(this);

        SetInputListenersEnabled(false);
        ReInput.players.GetPlayer(0).RemoveInputEventDelegate(OnUIBack);
        RequestHideHUDMessage.Send<RequestHideHUDMessage>(new RequestHideHUDMessageData { requestStatus = false, sender = this }, this);
        _isInteracting = false;
        ResetCurrentInteractorIdServerRPC(NetworkManager.LocalClientId);
    }

    private void SetMapEnabled(int categoryId, bool value)
    {
        var player = ReInput.players.GetPlayer(0);

        var maps = player.controllers.maps.GetAllMaps();

        foreach (var map in maps)
        {
            if (map.categoryId == categoryId)
                map.enabled = value;
        }
    }

    private void SetInputListenersEnabled(bool value)
    {
        foreach (var inputListener in _inputListeners)
        {
            inputListener.SetInputEnabled(value);
        }
    }
}
