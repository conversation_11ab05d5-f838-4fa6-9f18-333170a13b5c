using System.Collections.Generic;
using Consts;
using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;

[HelpURL(StringConsts.DocumentationLink + "NT-A-91")]
public class HumanSleeper : GenericGrabbable, ISnappable
{
    public NetworkRigidbodyExtended NetworkRigidbody => networkRigidbody;
    public bool CanBeSnapped => true;
    [SerializeField] private NetworkRigidbodyExtended networkRigidbody;

    public bool IsBeingSnapped { get; private set; }

    [SerializeField] private Transform hips;

    [SerializeField] private Animator animator;
    [SerializeField] private List<Rigidbody> rigidbodies = new();

    public static ItemsContainer<HumanSleeper> HumanSleepersData = new();
    private static readonly List<SkinnedMeshRenderer> _skinnedMeshRenderers = new();

    private void Awake()
    {
        if (networkRigidbody == null)
            networkRigidbody = GetComponent<NetworkRigidbodyExtended>();
    }

    public override void OnNetworkSpawn()
    {
        SetRagdollState(true);

        HumanSleepersData.AddItem(this);
    }

    public override bool RequestInteraction(InteractAction interactor)
    {
        return !IsBeingSnapped && base.RequestInteraction(interactor);
    }

    [ClientRpc]
    public void OnThrowIntoSlotClientRPC()
    {
        IsBeingSnapped = true;
        NetworkRigidbody.DisablePhysics();

        PurgeHipsOffset();

        SetRagdollState(false);

        animator.SetTrigger(AnimatorConsts.Encapsulate);
    }

    [Button]
    public void PurgeHipsOffset()
    {
        transform.SetPositionAndRotation(hips.position, hips.rotation);
        hips.SetLocalPositionAndRotation(Vector3.zero, Quaternion.identity);
    }

    public void OnLandedIntoSlotClientSide(bool autoSnapped)
    {

    }

    private void SetRagdollState(bool isRagdoll)
    {
        foreach (var rigidbody in rigidbodies)
            rigidbody.isKinematic = !isRagdoll;

        animator.enabled = !isRagdoll;
    }

    private void OnDisable()
    {
        HumanSleepersData.RemoveItem(this);
    }

    [Button]
    public void GetRigidbodies()
    {
        rigidbodies.Clear();
        GetComponentsInChildren<Rigidbody>(true, rigidbodies);
    }

    protected override void SetMainMaterial(Material material)
    {
        GetComponentsInChildren(_skinnedMeshRenderers);
        foreach (var skinnedMeshRenderer in _skinnedMeshRenderers)
        {
            _materials.Clear();
            _materials.AddRange(skinnedMeshRenderer.materials);
            _materials[0] = material;
            skinnedMeshRenderer.SetMaterials(_materials);
        }
    }

    protected override void SetRenderersLayer(int layer)
    {
        GetComponentsInChildren(_skinnedMeshRenderers);
        foreach (var meshRenderer in _skinnedMeshRenderers)
            meshRenderer.gameObject.layer = layer;
    }
}