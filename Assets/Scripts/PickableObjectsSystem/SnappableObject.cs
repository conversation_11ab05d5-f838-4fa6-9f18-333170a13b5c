using System.Collections.Generic;
using Consts;
using Sirenix.OdinInspector;
using Unity.Netcode;
using Unity.Netcode.Components;
using UnityEngine;

[HelpURL(StringConsts.DocumentationLink + "NT-A-88")]
public class SnappableObject : GenericGrabbable, ISnappable, IDecalSpawnHandler, IDecalDespawnHandler
{
    protected const string SnappableFoldoutGroup = "Snappable";

    /// <summary>
    /// Child slots for other snappables which are activated only after fixation
    /// </summary>
    [SerializeField, FoldoutGroup(SnappableFoldoutGroup)]
    private List<ItemSlot> childSlots = new();

    [SerializeField, FoldoutGroup(SnappableFoldoutGroup)]
    private NetworkRigidbodyExtended networkRigidbody;

    [SerializeField, FoldoutGroup(SnappableFoldoutGroup)]
    private Material snappedMaterialOverride;

    public NetworkRigidbodyExtended NetworkRigidbody => networkRigidbody;

    public static ItemsContainer<SnappableObject> SnappablesData = new();

    private static readonly Dictionary<SnappableObject, List<int>> DecalsRegistry = new();

    public bool CanBeSnapped => !DecalsRegistry.ContainsKey(this);

    public bool IsBeingSnapped { get; private set; }
    [ShowInInspector, ReadOnly] public bool IsSnapped { get; private set; }

    /// <summary>
    /// <inheritdoc cref="childSlots"/>
    /// </summary>
    public IEnumerable<ItemSlot> ChildSlots => childSlots;

    private void Awake()
    {
        if (networkRigidbody == null)
            networkRigidbody = GetComponent<NetworkRigidbodyExtended>();
    }

    public override void OnNetworkSpawn()
    {
        IsSnapped = false;
        IsBeingSnapped = false;

        // prefabs continue to be misconfigured
        RemoveNullChildSlots();
        SetChildSlotsActive(false);

        SnappablesData.AddItem(this);
    }

    public override bool RequestInteractionWithTool(InteractAction interactor, ITool toolInHands)
    {
        return !IsBeingSnapped && !IsSnapped && base.RequestInteractionWithTool(interactor, toolInHands);
    }

    public override bool RequestInteraction(InteractAction interactor)
    {
        return !IsBeingSnapped && !IsSnapped && base.RequestInteraction(interactor);
    }

    [ClientRpc]
    public void OnThrowIntoSlotClientRPC()
    {
        IsBeingSnapped = true;
        NetworkRigidbody.DisablePhysics();
    }

    // AC context
    public void OnLandedIntoSlotClientSide(bool autoSnapped)
    {
        SetChildSlotsActive(true);

        if (autoSnapped)
            foreach (var childSlot in childSlots)
                childSlot.AutoSnap();

        networkRigidbody.DisablePhysics();
        SnappablesData.RemoveItem(this);

        IsBeingSnapped = false;
        IsSnapped = true;

        SetMainMaterial(snappedMaterialOverride == null ? GameConfig.Instance.MaterialSettings.SnappedItemMaterial : snappedMaterialOverride);

        if (GameStateTracker.Instance.IsGameplay && GrabbableSFX != null && !GrabbableSFX.DropSound.IsNull)
            SoundFXManager.Instance.PlayOneShot(MultiplayerContext.AllClientsSide, GrabbableSFX.DropSound.Guid, transform.position);
    }

    public void Unsnap()
    {
        IsSnapped = false;
    }

    private void SetChildSlotsActive(bool active)
    {
        foreach (var child in childSlots)
            child.gameObject.SetActive(active);
    }

    protected override void OnCollisionEnter(Collision other)
    {
        if (GameStateTracker.Instance.IsGameplay)
            base.OnCollisionEnter(other);
    }

    private void OnDisable()
    {
        SnappablesData.RemoveItem(this);
    }

    public override void SetupGrabbablePrefab(Rigidbody rigidBody, NetworkTransform networkTransform, ItemData itemData)
    {
        base.SetupGrabbablePrefab(rigidBody, networkTransform, itemData);

        RemoveNullChildSlots();
    }

    public void RemoveNullChildSlots()
    {
        for (int i = childSlots.Count - 1; i >= 0; i--)
            if (childSlots[i] == null)
                childSlots.RemoveAt(i);
    }

    [Button]
    public void GetChildSlots()
    {
        GetComponentsInChildren(true, childSlots);
    }

    private void OnValidate()
    {
        this.CheckReference(ref networkRigidbody);
        GetChildSlots();
    }

    public override Color GetOutlineColor()
    {
        return Color.white;
    }

    public void OnDecalSpawn(int decalIndex)
    {
        RegisterDecal(decalIndex);
    }

    public void OnDecalDespawn(int decalIndex)
    {
        UnregisterDecal(decalIndex);
    }

    private void RegisterDecal(int decalIndex)
    {
        if (!DecalsRegistry.TryGetValue(this, out var decalsList))
        {
            decalsList = new List<int>();
            DecalsRegistry.Add(this, decalsList);
        }

        decalsList.Add(decalIndex);
    }

    private void UnregisterDecal(int decalIndex)
    {
        if (!DecalsRegistry.TryGetValue(this, out var decalsList)) return;

        decalsList.Remove(decalIndex);
        if (decalsList.Count == 0)
            DecalsRegistry.Remove(this);
    }
}