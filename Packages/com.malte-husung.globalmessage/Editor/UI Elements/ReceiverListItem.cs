// ©, 2022, Mal<PERSON> Husung

using System;

using UnityEngine.UIElements;

namespace MalteHusung.GlobalMessage.Editor
{
    internal class ReceiverListItem : ColumnListItem
    {
        public ObjectOrStringField ReceiverObjectElement = new ObjectOrStringField();
        public Label ReceiverActionNameElement = new Label();
        public Label RegistrationOrderElement = new Label();

        public UnityObjectOrStringName receiverObject
        {
            get => ReceiverObjectElement.value;
            set => ReceiverObjectElement.value = value;
        }

        public string receiverActionName
        {
            get => ReceiverActionNameElement.text;
            set => ReceiverActionNameElement.text = value;
        }
        
        public int registrationOrder
        {
            get => Int32.Parse(RegistrationOrderElement.text);
            set => RegistrationOrderElement.text = "" + value;
        }

        public void SetData(EditorInfo.IReceiverInfo info)
        {
            receiverObject = info.ReceiverObjectOrName;
            receiverActionName = info.ReceiverActionName;
            registrationOrder = info.RegistrationOrder;
        }
        
        public ReceiverListItem() : base(3)
        {
            style.height = new StyleLength(20f);
            ReceiverObjectElement.ObjectFieldElement.MakeReadOnly();
            var pingWrapper = UIToolkitHelper.CreateClickToPingWrapper(ReceiverObjectElement.ObjectFieldElement, true, false);
            pingWrapper.Add(ReceiverObjectElement);
            // dnd blocker has to be in front
            ReceiverObjectElement.SendToBack();
            
            GetColumnElement(0).Add(pingWrapper);
            GetColumnElement(1).Add(ReceiverActionNameElement);
            GetColumnElement(2).Add(RegistrationOrderElement);
        }
        
        public new class UxmlFactory : UxmlFactory<ReceiverListItem, UxmlTraits>
        {
        }
    }
}


