<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="True">
    <uie:Toolbar name="columnControls" style="border-left-width: 1px; border-right-width: 1px;">
        <MalteHusung.GlobalMessage.Editor.TwoPaneSplitViewUiBuilder style="min-width: 100px;">
            <uie:ToolbarMenu text="Oder Option 1" />
            <MalteHusung.GlobalMessage.Editor.TwoPaneSplitViewUiBuilder>
                <uie:ToolbarMenu display-tooltip-when-elided="true" text="Option 2" />
                <uie:ToolbarMenu display-tooltip-when-elided="true" text="Option 3" />
            </MalteHusung.GlobalMessage.Editor.TwoPaneSplitViewUiBuilder>
        </MalteHusung.GlobalMessage.Editor.TwoPaneSplitViewUiBuilder>
    </uie:Toolbar>
    <ui:ListView focusable="true" show-border="true" show-alternating-row-backgrounds="All" header-title="Message Receivers" show-bound-collection-size="false" virtualization-method="FixedHeight" />
</ui:UXML>
