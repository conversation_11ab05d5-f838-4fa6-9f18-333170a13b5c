// ©, 2022, <PERSON><PERSON>g

using System;

namespace MalteHusung.GlobalMessage
{
    /// <summary>
    /// Add this attribute to your message types to control their level of integration into the editor.
    /// </summary>
    [AttributeUsage(AttributeTargets.Class, Inherited = false)]
    public class MessageTypeEditorSettingsAttribute : Attribute
    {
        /// <summary>
        /// <para>
        /// If set to false, the message type will not be visible in GlobalMessage's editor windows and messages can not
        /// be sent directly from editor windows (sending from <see cref="MessageSender"/> is not affected).
        /// </para>
        /// This is useful for messages that are not directly game related e.g. messages used in editor scripting
        /// </summary>
        public bool UseEditorFeatures { get; set; } = true;

        /// <summary>
        /// <para>
        /// If set to false, no editor message object (emo) for this message type is used by GlobalMessage.
        /// </para>
        /// This means no emo is displayed in the editor windows, no emo is created automatically,
        /// no emo is kept up to date (i.e. type renames not reflected).
        /// <remarks>
        /// Editor message objects that already exist for the
        /// message type are not deleted. That has to be done by hand in the project.
        /// </remarks>
        /// </summary>
        public bool UseEditorMessageObject { get; set; } = true;

        /// <summary>
        /// Is displayed in editor to give users more info about this message type. 
        /// </summary>
        public string Description { get; set; } = null;
    }
}
